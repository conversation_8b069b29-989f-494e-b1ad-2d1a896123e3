<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.inflate" id="avfilter.inflate">
    <name>Inflate</name>
    <description>Rreplaces the pixel by the local(3x3) average by taking into account only values higher than the pixel. </description>
    <author>libavfilter</author>
    <parameter type="constant" name="av.threshold0" default="65535" min="0" max="65535" factor="1">
        <name>threshold0</name>
    </parameter>
    <parameter type="constant" name="av.threshold1" default="65535" min="0" max="65535" factor="1">
        <name>threshold1</name>
    </parameter>
    <parameter type="constant" name="av.threshold2" default="65535" min="0" max="65535" factor="1">
        <name>threshold2</name>
    </parameter>
    <parameter type="constant" name="av.threshold3" default="65535" min="0" max="65535" factor="1">
        <name>threshold3</name>
    </parameter>
</effect>
