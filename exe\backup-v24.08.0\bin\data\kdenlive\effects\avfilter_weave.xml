<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.weave" id="avfilter.weave">
    <name>Weave interlacer</name>
    <description>The weave takes a field-based video input and join each two sequential fields into single frame, producing a new double height clip with half the frame rate and half the frame count.</description>
    <author>libavfilter</author>
    <parameter type="list" name="av.first_field" default="t" paramlist="t;b">
        <paramlistdisplay>Top,Bottom</paramlistdisplay>
        <name>First Field</name>
    </parameter>
</effect>
