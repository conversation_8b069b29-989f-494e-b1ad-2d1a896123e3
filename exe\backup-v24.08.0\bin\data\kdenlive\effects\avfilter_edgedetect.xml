<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.edgedetect" id="avfilter.edgedetect">
    <name>Edge detection</name>
    <description>Detect and draw edges. The filter uses the Canny Edge Detection algorithm. </description>
    <author>libavfilter</author>
    <parameter type="constant" name="av.low" default="0.078" min="0" max="1" decimals="3">
        <name>Low threshold</name>
    </parameter>
    <parameter type="constant" name="av.high" default="0.196" min="0" max="1" decimals="3">
        <name>High threshold</name>
    </parameter>
    <parameter type="list" name="av.mode" default="wires" paramlist="wires;colormix;canny">
        <paramlistdisplay>Wires,Colormix,Canny</paramlistdisplay>
        <name>Modes</name>
    </parameter>
    <parameter type="list" name="av.planes" default="7" paramlist="0;1;2;3;4;5;6;7;8">
        <paramlistdisplay>None,Y,U,YU,V,YV,UV,YUV,Alpha</paramlistdisplay>
        <name>Planes</name>
    </parameter>
</effect>
