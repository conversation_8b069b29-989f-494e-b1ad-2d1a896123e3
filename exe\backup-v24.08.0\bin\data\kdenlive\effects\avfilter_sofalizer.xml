<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.sofalizer" id="avfilter.sofalizer" type="audio">
    <name>SOFAlizer</name>
    <description>SOFAlizer uses head-related transfer functions (HRTFs) to create virtual loudspeakers around the user for binaural listening via headphones (audio formats up to 9 channels supported). The HRTFs are stored in SOFA files (see http://www.sofacoustics.org/ for a database). SOFAlizer is developed at the Acoustics Research Institute (ARI) of the Austrian Academy of Sciences. </description>
    <author>libavfilter</author>
    <parameter type="url" name="av.sofa" filter="*.sofa">
        <name>SOFA file</name>
    </parameter>
    <parameter type="constant" name="av.gain" default="0" min="-20" max="20" suffix="dB">
        <name>Gain</name>
    </parameter>
    <parameter type="constant" name="av.rotation" default="0" min="-360" max="360" suffix="°">
        <name>Rotation</name>
    </parameter>
    <parameter type="constant" name="av.elevation" default="0" min="-360" max="360" suffix="°">
        <name>Elevation</name>
    </parameter>
    <parameter type="constant" name="av.radius" default="1" min="0" max="100" decimals="2">
        <name>Radius</name>
    </parameter>
    <parameter type="list" name="av.type" default="freq" paramlist="freq;time">
        <paramlistdisplay>Frequency,Time</paramlistdisplay>
        <name>Type</name>
    </parameter>
    <parameter type="constant" name="av.lfegain" default="0" min="-20" max="20" suffix="dB">
        <name>LFE Gain</name>
    </parameter>
    <parameter type="constant" name="av.framesize" default="1024" min="1024" max="96000" suffix="samples">
        <name>Framesize</name>
        <comment>Use if option ‘type’ is set to freq.</comment>
    </parameter>
    <parameter type="bool" name="av.normalize" default="1">
        <name>Normalize</name>
    </parameter>
    <parameter type="bool" name="av.interpolate" default="0">
        <name>Interpolate</name>
    </parameter>
    <parameter type="bool" name="av.minphase" default="0">
        <name>Minphase</name>
    </parameter>
    <parameter type="bool" name="av.anglestep" default="0">
        <name>Anglestep</name>
        <comment>Only used if option interpolate is enabled.</comment>
    </parameter>
    <parameter type="bool" name="av.radstep" default="0">
        <name>Radstep</name>
        <comment>Only used if option interpolate is enabled.</comment>
    </parameter>
</effect>
