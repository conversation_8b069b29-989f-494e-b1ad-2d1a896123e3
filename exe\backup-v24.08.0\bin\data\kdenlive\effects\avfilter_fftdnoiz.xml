<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.fftdnoiz" id="avfilter.fftdnoiz">
    <name>3D FFT Denoiser</name>
    <description>Denoise frames using 3D FFT (frequency domain filtering)</description>
    <author>libavfilter</author>
    <parameter type="constant" name="av.sigma" default="1" min="0" max="30" factor="1">
        <name>Sigma</name>
    </parameter>
    <parameter type="constant" name="av.amount" default="1" min="0.01" max="1" decimals="3">
        <name>Amount</name>
    </parameter>
    <parameter type="constant" name="av.block" default="4" min="3" max="6" factor="1">
        <name>Block</name>
    </parameter>
    <parameter type="constant" name="av.overlap" default="0.5" min="0.2" max="0.8" decimals="3">
        <name>Overlap</name>
    </parameter>
    <parameter type="bool" name="av.prev" default="0">
        <name>Add previous frame to temporal denoise</name>
    </parameter>
    <parameter type="bool" name="av.next" default="0">
        <name>Add next frame to temporal denoise</name>
    </parameter>
    <parameter type="list" name="av.planes" default="7" paramlist="0;1;2;3;4;5;6;7;8">
        <paramlistdisplay>None,Y,U,YU,V,YV,UV,YUV,Alpha</paramlistdisplay>
        <name>Planes</name>
    </parameter>
</effect>
