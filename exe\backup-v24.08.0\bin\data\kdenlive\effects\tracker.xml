<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="opencv.tracker" id="opencv.tracker" condition="results" version="2">
    <name>Motion Tracker</name>
    <description>Select a zone to follow its movements</description>
    <author><PERSON><PERSON><PERSON></author>
    <parameter type="animatedrect" name="results" conditional="1" opacity="false" default="50% 50% 25% 25%">
        <name>Rectangle</name>
    </parameter>
    <parameter type="listdependency" name="algo" default="KCF" paramlist="KCF;CSRT;MOSSE;MIL;MEDIANFLOW;DaSIAM;Nano" conditional="1">
        <paramlistdisplay>KCF,CSRT,MOSSE,MIL,MedianFlow,DaSIAM,Nano</paramlistdisplay>
        <name>Tracker algorithm</name>
        <paramdependencies value="DaSIAM" files="dasiamrpn_model.onnx;dasiamrpn_kernel_r1.onnx;dasiamrpn_kernel_cls1.onnx" folder="/opencvmodels">Required model files for DaSiam Tracker not found in &lt;a href="file://%folder"&gt;models folder&lt;/a&gt;. Check our &lt;a href="https://docs.kdenlive.org/en/effects_and_compositions/video_effects/alpha_mask_keying/motion_tracker.html#dasiam"&gt;manual&lt;/a&gt; for instructions.</paramdependencies>
        <paramdependencies value="Nano" files="nanotrack_backbone_sim.onnx;nanotrack_head_sim.onnx" folder="/opencvmodels">Requires OpenCV >= 4.7. Required model files for Nano Tracker not found in &lt;a href="file://%folder"&gt;models folder&lt;/a&gt;. Check our &lt;a href="https://docs.kdenlive.org/en/effects_and_compositions/video_effects/alpha_mask_keying/motion_tracker.html#nano"&gt;manual&lt;/a&gt; for instructions.</paramdependencies>
    </parameter>
    <parameter type="constant" name="steps" default="5" min="0" max="120">
        <name>Keyframes spacing</name>
        <comment>Defines the frequency of stored keyframes. A keyframe is created every steps frames.</comment>
    </parameter>
    <parameter type="list" name="shape" default="0" paramlist="0;1;2">
        <paramlistdisplay>Rectangle,Ellipse,Arrow</paramlistdisplay>
        <name>Frame shape</name>
    </parameter>
    <parameter type="constant" name="shape_width" max="100" min="-1" default="6" suffix="pixels">
        <name>Shape width</name>
    </parameter>
    <parameter type="fixedcolor" name="shape_color" default="0xff0000ff">
        <name>Shape color</name>
    </parameter>
    <parameter type="constant" name="blur" max="200" min="0" default="20">
        <name>Blur</name>
    </parameter>
    <parameter type="list" name="blur_type" default="4" paramlist="0;1;2;3;4">
        <paramlistdisplay>Median Blur,Gaussian Blur,Pixelate,Opaque fill,None</paramlistdisplay>
        <name>Blur type</name>
    </parameter>
    <parameter type="hidden" name="modelsfolder" default="">
    </parameter>
    <parameter type="hidden" name="rect" default="50% 50% 25% 25%">
    </parameter>
    <parameter type="filterjob" filtertag="opencv.tracker" consumer="null" consumerparams="all=1 terminate_on_pause=1 audio_off=1 no_meta=1 real_time=-1">
        <name conditional="Reset">Analyse to Apply Effect</name>
        <jobparam name="conditionalinfo">Filter is in preview mode. Click Analyse to see real effect</jobparam>
        <jobparam name="key">results</jobparam>
        <jobparam name="keydefault">50% 50% 25% 25%</jobparam>
        <jobparam name="finalfilter">opencv.tracker</jobparam>
        <jobparam name="relativeInOut">1</jobparam>
        <jobparam name="animated">1</jobparam>
        <jobparam name="displaydataname">Motion tracking</jobparam>
    </parameter>
</effect>
