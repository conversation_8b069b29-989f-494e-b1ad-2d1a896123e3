<!-- This file can be used to include the notice in documentation 
     <!DOCTYPE book ... [
      <!ENTITY % FDLIS "INCLUDE">
      <!ENTITY FDLISTitles "title 1, title 2, title 3">
      <!ENTITY % FDLFCT "INCLUDE">
      <!ENTITY FDLFCTTitles "title 4, title 5, title 6">
      <!ENTITY % FDLBCT "INCLUDE">
      <!ENTITY FDLBCTTitles "title 7, title 7b">
      <!ENTITY % ents "-//KDE//ENTITIES Application-Variable Entities V1.0//EN">
      %ents;
      ...
     ]>
    ...
     <bookinfo>
       <legalnotice>
         <para>Copyright (C) 20yy  [name of author]</para>
         &FDLnotice;
       </legalnotice>
     </bookinfo>
    ...

    Alternatively, you can include the text literally.

    Include a copy of the license in the documentation distribution.
 -->
<para>Permission is granted to copy, distribute and/or modify this
document under the terms of the GNU Free Documentation License,
Version 1.2 or any later version published by the Free Software
Foundation; with &FDLInvariantSections;, with &FDLFrontCoverText;, and
with &FDLBackCoverText;.  A copy of the license is included in <xref linkend="gnu-fdl"/>.</para>

<!-- If you have no Invariant Sections, don't add any FDLIS entities.
     If you have no Front-Cover Texts, don't add any FDLFCT; likewise
     for Back-Cover Texts (the SGML setup takes care of complying with
     the GNU requirements).  Adding the entities: if you use any, then
     add FDL* with value "INCLUDE" and FDL*Titles with a list of
     titles.  You will get the default GNU template text if you don't
     specify the FDL*Titles entity after specifying the FDL* entity.
     This will only have effect when you specify %FDLSlots; _after_
     the entity definitions.

     If your document contains nontrivial examples of program code, we
     recommend releasing these examples in parallel under your choice
     of free software license, such as the GNU General Public License,
     to permit their use in free software.
 -->
<!--
Local variables:
mode: sgml
sgml-general-insert-case: lower
sgml-parent-document: ("index.docbook" "book" "bookinfo" "legalnotice" "para")
End:
-->
