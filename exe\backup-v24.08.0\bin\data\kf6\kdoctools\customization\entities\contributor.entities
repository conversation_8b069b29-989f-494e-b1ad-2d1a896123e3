<!--
    KDE general entities for DocBook as used in the KDE documentation
    
    SPDX-FileCopyrightText: 2002 <PERSON><PERSON><PERSON>

    SPDX-License-Identifier: GPL-2.0-or-later
    
    Send suggestions, comments, etc. to the KDE docbook list 
    <<EMAIL>>.

    USAGE

    Refer to this file as

      "-//KDE//ENTITIES DocBook XML General Entity Declarations (Persons) V1.0//EN"

    This file contains what dbgenent.mod should contain and is read
    after all other files just like dbgenent.mod should be.

-->

<!-- ============================================================= -->
<!--		    Extensions to the DocBook DTD		   -->
<!-- ============================================================= -->
<!-- This is meant for entities that define person names and 
     their contact mail addresses.
     Note that these definitions will override language-specific ones
     if there are any.  Translators should use the 
     contributor.entities file for their language.
     Please keep the list sorted on the family name                
                                                                   -->


<!ENTITY <PERSON>.E.Ahlquist.Jr '<personname><firstname>Paul</firstname><othername>E.</othername><surname>Ahlquist</surname><lineage>Jr.</lineage></personname>'>
<!ENTITY Paul.E.Ahlquist.Jr.mail '<email><EMAIL></email>'>
<!ENTITY Danny.Allen '<personname><firstname>Danny</firstname><surname>Allen</surname></personname>'>
<!ENTITY Danny.Allen.mail '<email><EMAIL></email>'>
<!ENTITY Sandro.Andrade '<personname><firstname>Sandro</firstname><surname>Andrade</surname></personname>'>
<!ENTITY Sandro.Andrade.mail '<email><EMAIL></email>'>
<!ENTITY Elvis.Angelaccio '<personname><firstname>Elvis</firstname><surname>Angelaccio</surname></personname>'>
<!ENTITY Elvis.Angelaccio.mail '<email><EMAIL></email>'>
<!ENTITY Primoz.Anzur '<personname><firstname>Primoz</firstname><surname>Anzur</surname></personname>'>
<!ENTITY Primoz.Anzur.mail '<email><EMAIL></email>'>
<!ENTITY Ewald.Arnold '<personname><firstname>Ewald</firstname><surname>Arnold</surname></personname>'>
<!ENTITY Ewald.Arnold.mail '<email><EMAIL></email>'>
<!ENTITY Yves.Arrouye '<personname><firstname>Yves</firstname><surname>Arrouye</surname></personname>'>
<!ENTITY Yves.Arrouye.mail '<email><EMAIL></email>'>
<!ENTITY Albert.Astals.Cid '<personname><firstname>Albert</firstname><surname>Astals</surname></personname>'>
<!ENTITY Albert.Astals.Cid.mail '<email><EMAIL></email>'>

<!ENTITY Marc.Bartsch '<personname><firstname>Marc</firstname><surname>Bartsch</surname></personname>'>
<!ENTITY Marc.Bartsch.mail '<email><EMAIL></email>'>
<!ENTITY Waldo.Bastian '<personname><firstname>Waldo</firstname><surname>Bastian</surname></personname>'>
<!ENTITY Waldo.Bastian.mail '<email><EMAIL></email>'>
<!ENTITY Thomas.Baumgart '<personname><firstname>Thomas</firstname><surname>Baumgart</surname></personname>'>
<!ENTITY Thomas.Baumgart.mail '<email><EMAIL></email>'>
<!ENTITY Andreas.Beckermann '<personname><firstname>Andreas</firstname><surname>Beckermann</surname></personname>'>
<!ENTITY Andreas.Beckermann.mail '<email><EMAIL></email>'>
<!ENTITY Orville.Bennett '<personname><firstname>Orville</firstname><surname>Bennett</surname></personname>'>
<!ENTITY Orville.Bennett.mail '<email><EMAIL></email>'>
<!ENTITY Nick.Betcher '<personname><firstname>Nick</firstname><surname>Betcher</surname></personname>'>
<!ENTITY Nick.Betcher.mail '<email><EMAIL></email>'>
<!ENTITY Stephan.Binner '<personname><firstname>Stephan</firstname><surname>Binner</surname></personname>'>
<!ENTITY Stephan.Binner.mail '<email><EMAIL></email>'>
<!ENTITY Eric.Bischoff '<personname><firstname>Éric</firstname><surname>Bischoff</surname></personname>'>
<!ENTITY Eric.Bischoff.mail '<email><EMAIL></email>'>
<!ENTITY Tony.Bloomfield '<personname><firstname>Tony</firstname><surname>Bloomfield</surname></personname>'>
<!ENTITY Tony.Bloomfield.mail '<email><EMAIL></email>'>
<!ENTITY Aron.Bostrom '<personname><firstname>Aron</firstname><surname>Boström</surname></personname>'>
<!ENTITY Aron.Bostrom.mail '<email><EMAIL></email>'>
<!ENTITY Michel.Boyer '<personname><firstname>Michel</firstname><surname>Boyer de la Giroday</surname></personname>'>
<!ENTITY Michel.Boyer.mail '<email><EMAIL></email>'>
<!ENTITY Michael.Brade '<personname><firstname>Michael</firstname><surname>Brade</surname></personname>'>
<!ENTITY Michael.Brade.mail '<email><EMAIL></email>'>
<!ENTITY Preston.Brown '<personname><firstname>Preston</firstname><surname>Brown</surname></personname>'>
<!ENTITY Preston.Brown.mail '<email><EMAIL></email>'>
<!ENTITY David.Bryant '<personname><firstname>David</firstname><surname>Bryant</surname></personname>'>
<!ENTITY David.Bryant.mail '<email><EMAIL></email>'>
<!ENTITY Oswald.Buddenhagen '<personname><firstname>Oswald</firstname><surname>Buddenhagen</surname></personname>'>
<!ENTITY Oswald.Buddenhagen.mail '<email><EMAIL></email>'>

<!ENTITY Paul.Campbell '<personname><firstname>Paul</firstname><surname>Campbell</surname></personname>'>
<!ENTITY Paul.Campbell.mail '<email><EMAIL></email>'>
<!ENTITY Javier.J.Campos '<personname><firstname>Javier</firstname><othername>J.</othername><surname>Campos</surname></personname>'>
<!ENTITY Javier.J.Campos.mail '<email><EMAIL></email>'>
<!ENTITY Robert.Cimrman '<personname><firstname>Robert</firstname><surname>Cimrman</surname></personname>'>
<!ENTITY Robert.Cimrman.mail '<email><EMAIL></email>'>
<!ENTITY John.Cirillo '<personname><firstname>John</firstname><surname>Cirillo</surname></personname>'>
<!ENTITY John.Cirillo.mail '<email><!-- FIXME --></email>'>
<!ENTITY Scarlett.Clark '<personname><firstname>Scarlett</firstname><surname>Clark</surname></personname>'>
<!ENTITY Scarlett.Clark.mail '<email><EMAIL></email>'>
<!ENTITY Raphael.Kubo.da.Costa '<personname><firstname>Raphael</firstname><othername>Kubo da</othername><surname>Costa</surname></personname>'>
<!ENTITY Raphael.Kubo.da.Costa.mail '<email><EMAIL></email>'>
<!ENTITY Claudiu.Costin '<personname><firstname>Claudiu</firstname><surname>Costin</surname></personname>'>
<!ENTITY Claudiu.Costin.mail '<email><EMAIL></email>'>
<!ENTITY Neal.Crook '<personname><firstname>Neal</firstname><surname>Crook</surname></personname>'>
<!ENTITY Neal.Crook.mail '<email><EMAIL></email>'>
<!ENTITY Christoph.Cullmann '<personname><firstname>Christoph</firstname><surname>Cullmann</surname></personname>'>
<!ENTITY Christoph.Cullmann.mail '<email><EMAIL></email>'>

<!ENTITY Fabian.Dal.Santo '<personname><firstname>Fabian</firstname><surname>Dal Santo</surname></personname>'>
<!ENTITY Matthias.Kalle.Dalheimer '<personname><firstname>Matthias</firstname><othername>Kalle</othername><surname>Dalheimer</surname></personname>'>
<!ENTITY Matthias.Kalle.Dalheimer.mail '<email><EMAIL></email>'>
<!ENTITY Fabian.DalSanto.mail '<email><EMAIL></email>'>
<!ENTITY Helge.Deller '<personname><firstname>Helge</firstname><surname>Deller</surname></personname>'>
<!ENTITY Helge.Deller.mail '<email><EMAIL></email>'>
<!ENTITY Pablo.de.Vicente '<personname><firstname>Pablo</firstname><surname>de Vicente</surname></personname>'>
<!ENTITY Pablo.de.Vicente.mail '<email><EMAIL></email>'>
<!ENTITY Thomas.Diehl '<personname><firstname>Thomas</firstname><surname>Diehl</surname></personname>'>
<!ENTITY Thomas.Diehl.mail '<email><EMAIL></email>'>
<!ENTITY Lars.Doelle '<personname><firstname>Lars</firstname><surname>Doelle</surname></personname>'>
<!ENTITY Lars.Doelle.mail '<email><EMAIL></email>'>
<!ENTITY Dirk.Doerflinger '<personname><firstname>Dirk</firstname><surname>Doerflinger</surname></personname>'>
<!ENTITY Dirk.Doerflinger.mail '<email><EMAIL></email>'>
<!ENTITY Mark.Donohoe '<personname><firstname>Mark</firstname><surname>Donohoe</surname></personname>'>
<!ENTITY Mark.Donohoe.mail '<email><EMAIL></email>'>
<!ENTITY Patrick.Dowler '<personname><firstname>Pat</firstname><surname>Dowler</surname></personname>'>
<!ENTITY Patrick.Dowler.mail '<email><EMAIL></email>'>
<!ENTITY Jonathan.Drews '<personname><firstname>Jonathan</firstname><surname>Drews</surname></personname>'>
<!ENTITY Jonathan.Drews.mail '<email><EMAIL></email>'>
<!ENTITY Craig.Drummond '<personname><firstname>Craig</firstname><surname>Drummond</surname></personname>'>
<!ENTITY Craig.Drummond.Mail '<email><EMAIL></email>'>
<!ENTITY Daniel.M.Duley '<personname><firstname>Daniel</firstname><othername>M.</othername><surname>Duley</surname></personname>'>
<!ENTITY Daniel.M.Duley.mail '<email><EMAIL></email>'>
<!ENTITY Alexander.Dymo '<personname><firstname>Alexander</firstname><surname>Dymo</surname></personname>'>
<!ENTITY Alexander.Dymo.mail '<email><EMAIL></email>'>

<!ENTITY Michael.T.Edwardes '<personname><firstname>Michael</firstname><othername>T.</othername><surname>Edwardes</surname></personname>'>
<!ENTITY Michael.T.Edwardes.mail '<email><EMAIL></email>'>
<!ENTITY Matthias.Elter '<personname><firstname>Matthias</firstname><surname>Elter</surname></personname>'>
<!ENTITY Matthias.Elter.mail '<email><EMAIL></email>'>
<!ENTITY Matthias.Ettrich '<personname><firstname>Matthias</firstname><surname>Ettrich</surname></personname>'>
<!ENTITY Matthias.Ettrich.mail '<email><EMAIL></email>'>
<!ENTITY Heiko.Evermann '<personname><firstname>Heiko</firstname><surname>Evermann</surname></personname>'>
<!ENTITY Heiko.Evermann.mail '<email><EMAIL></email>'>

<!ENTITY David.Faure '<personname><firstname>David</firstname><surname>Faure</surname></personname>'>
<!ENTITY David.Faure.mail '<email><EMAIL></email>'>
<!ENTITY John.Firebaugh '<personname><firstname>John</firstname><surname>Firebaugh</surname></personname>'>
<!ENTITY John.Firebaugh.mail '<email><EMAIL></email>'>
<!ENTITY Dario.Freddi '<personname><firstname>Dario</firstname><surname>Freddi</surname></personname>'>
<!ENTITY Dario.Freddi.mail '<email><EMAIL></email>'>
<!ENTITY Klaus.Freitag '<personname><firstname>Klaus</firstname><surname>Freitag</surname></personname>'>
<!ENTITY Klaus.Freitag.mail '<email><EMAIL></email>'>
<!ENTITY Haavard.Froeiland '<personname><firstname>Haavard</firstname><surname>Froeiland</surname></personname>'>
<!ENTITY Haavard.Froeiland.mail '<email><EMAIL></email>'>

<!ENTITY Karl.Garrison '<personname><firstname>Karl</firstname><surname>Garrison</surname></personname>'>
<!ENTITY Karl.Garrison.mail '<email><EMAIL></email>'>
<!ENTITY Ferdinand.Gassauer '<personname><firstname>Ferdinand</firstname><surname>Gassauer</surname></personname>'>
<!ENTITY Ferdinand.Gassauer.mail '<email><EMAIL></email>'>
<!ENTITY Christian.Gebauer '<personname><firstname>Christian</firstname><surname>Gebauer</surname></personname>'>
<!ENTITY Christian.Gebauer.mail '<email><EMAIL></email>'>
<!ENTITY Bernd.Gehrmann '<personname><firstname>Bernd</firstname><surname>Gehrmann</surname></personname>'>
<!ENTITY Bernd.Gehrmann.mail '<email><EMAIL></email>'>
<!ENTITY Francis.Giannaros '<personname><firstname>Francis</firstname><surname>Giannaros</surname></personname>'>
<!ENTITY Francis.Giannaros.mail '<email><EMAIL></email>'>
<!ENTITY Frederik.Gladhorn '<personname><firstname>Frederik</firstname><surname>Gladhorn</surname></personname>'>
<!ENTITY Frederik.Gladhorn.mail '<email><EMAIL></email>'>
<!ENTITY Michael.Goffioul '<personname><firstname>Michael</firstname><surname>Goffioul</surname></personname>'>
<!ENTITY Michael.Goffioul.mail '<email><EMAIL></email>'>
<!ENTITY Robert.Gogolok '<personname><firstname>Robert</firstname><surname>Gogolok</surname></personname>'>
<!ENTITY Robert.Gogolok.mail '<email><EMAIL></email>'>
<!ENTITY Nicolas.Goutte '<personname><firstname>Nicolas</firstname><surname>Goutte</surname></personname>'>
<!ENTITY Nicolas.Goutte.mail '<email><EMAIL></email>'>
<!ENTITY Martin.Graesslin '<personname><firstname>Martin</firstname><surname>Gräßlin</surname></personname>'>
<!ENTITY Martin.Graesslin.mail '<email><EMAIL></email>'>
<!ENTITY Kurt.Granroth '<personname><firstname>Kurt</firstname><surname>Granroth</surname></personname>'>
<!ENTITY Kurt.Granroth.mail '<email><EMAIL></email>'>
<!ENTITY Wilco.Greven '<personname><firstname>Wilco</firstname><surname>Greven</surname></personname>'>
<!ENTITY Wilco.Greven.mail '<email><EMAIL></email>'>
<!ENTITY Andreas.Gungl '<personname><firstname>Andreas</firstname><surname>Gungl</surname></personname>'>
<!ENTITY Andreas.Gungl.mail '<email><EMAIL></email>'>
<!ENTITY Boudhayan.Gupta '<personname><firstname>Boudhayan</firstname><surname>Gupta</surname></personname>'>
<!ENTITY Boudhayan.Gupta.mail '<email><EMAIL></email>'>

<!ENTITY Nicolas.Hadacek '<personname><firstname>Nicolas</firstname><surname>Hadacek</surname></personname>'>
<!ENTITY Nicolas.Hadacek.mail '<email><EMAIL></email>'>
<!ENTITY Michael.Haeckel '<personname><firstname>Michael</firstname><surname>Haeckel</surname></personname>'>
<!ENTITY Michael.Haeckel.mail '<email><EMAIL></email>'>
<!ENTITY Duncan.Haldane '<personname><firstname>Duncan</firstname><surname>Haldane</surname></personname>'>
<!ENTITY Duncan.Haldane.mail '<email><EMAIL></email>'>
<!ENTITY J.Hall '<personname><firstname>J</firstname><surname>Hall</surname></personname>'>
<!ENTITY J.Hall.mail '<email><EMAIL></email>'>
<!ENTITY Erlend.Hamberg '<personname><firstname>Erlend</firstname><surname>Hamberg</surname></personname>'>
<!ENTITY Erlend.Hamberg.mail '<email><EMAIL></email>'>
<!ENTITY Steffen.Hansen '<personname><firstname>Steffen</firstname><surname>Hansen</surname></personname>'>
<!ENTITY Steffen.Hansen.mail '<email><EMAIL></email>'>
<!ENTITY Brad.Hards '<personname><firstname>Brad</firstname><surname>Hards</surname></personname>'>
<!ENTITY Brad.Hards.mail '<email><EMAIL></email>'>
<!ENTITY Jason.Harris '<personname><firstname>Jason</firstname><surname>Harris</surname></personname>'>
<!ENTITY Jason.Harris.mail '<email><EMAIL></email>'>
<!ENTITY Dominik.Haumann '<personname><firstname>Dominik</firstname><surname>Haumann</surname></personname>'>
<!ENTITY Dominik.Haumann.mail '<email><EMAIL></email>'>
<!ENTITY Simon.Hausmann '<personname><firstname>Simon</firstname><surname>Hausmann</surname></personname>'>
<!ENTITY Simon.Hausmann.mail '<email><EMAIL></email>'>
<!ENTITY Richard.Hawthorne '<personname><firstname>Richard</firstname><surname>Hawthorne</surname></personname>'>
<!ENTITY Richard.Hawthorne.mail '<email><EMAIL></email>'>
<!ENTITY Kurt.Hindenburg '<personname><firstname>Kurt</firstname><surname>Hindenburg</surname></personname>'>
<!ENTITY Kurt.Hindenburg.mail '<email><EMAIL></email>'>
<!ENTITY Matthias.Hoelzer-Kluepfel '<personname><firstname>Matthias</firstname><surname>H&ouml;lzer-Kl&uuml;pfel</surname></personname>'>
<!ENTITY Matthias.Hoelzer-Kluepfel.mail '<email><EMAIL></email>'>
<!ENTITY Rik.Hemsley '<personname><firstname>Rik</firstname><surname>Hemsley</surname></personname>'>
<!ENTITY Rik.Hemsley.mail '<email><EMAIL></email>'>
<!ENTITY Martin.Heni '<personname><firstname>Martin</firstname><surname>Heni</surname></personname>'>
<!ENTITY Martin.Heni.mail '<email><EMAIL></email>'>
<!ENTITY Hauke.Hildebrandt '<personname><firstname>Hauke</firstname><surname>Hildebrandt</surname></personname>'>
<!ENTITY Hauke.Hildebrandt.mail '<email><EMAIL></email>'>
<!ENTITY Jens.Hoefkens '<personname><firstname>Jens</firstname><surname>Hoefkens</surname></personname>'>
<!ENTITY Jens.Hoefkens.mail '<email><EMAIL></email>'>
<!ENTITY TC.Hollingsworth '<personname><firstname>T.C.</firstname><surname>Hollingsworth</surname></personname>'>
<!ENTITY TC.Hollingsworth.mail '<email><EMAIL></email>'>
<!ENTITY Mark.Holloman '<personname><firstname>Mark</firstname><surname>Holloman</surname></personname>'>
<!ENTITY Mark.Holloman.mail '<email><EMAIL></email>'>
<!ENTITY Greg.M.Holmes '<personname><firstname>Greg</firstname><othername>M.</othername><surname>Holmes</surname></personname>'>
<!ENTITY Greg.M.Holmes.mail '<email><!-- FIXME --></email>'>
<!ENTITY Chris.Howells '<personname><firstname>Chris</firstname><surname>Howells</surname></personname>'>
<!ENTITY Chris.Howells.mail '<email><EMAIL></email>'>
<!ENTITY Harald.Hvaal '<personname><firstname>Harald</firstname><surname>Hvaal</surname></personname>'>
<!ENTITY Harald.Hvaal.mail '<email><EMAIL></email>'>

<!ENTITY Geert.Jansen '<personname><firstname>Geert</firstname><surname>Jansen</surname></personname>'>
<!ENTITY Geert.Jansen.mail '<email><EMAIL></email>'>
<!ENTITY David.Jarvie '<personname><firstname>David</firstname><surname>Jarvie</surname></personname>'>
<!ENTITY David.Jarvie.mail '<email><EMAIL></email>'>
<!ENTITY Stephan.Johach '<personname><firstname>Stephan</firstname><surname>Johach</surname></personname>'>
<!ENTITY Stephan.Johach.mail '<email><EMAIL></email>'>
<!ENTITY Antonio.Larrosa.Jimenez '<personname><firstname>Antonio</firstname><othername>Larrosa</othername><surname>Jim&eacute;nez</surname></personname>'>
<!ENTITY Antonio.Larrosa.Jimenez.mail '<email><EMAIL></email>'>
<!ENTITY David.Johnson '<personname><firstname>David</firstname><surname>Johnson</surname></personname>'>
<!ENTITY David.Johnson.mail '<email><EMAIL></email>'>
<!ENTITY Richard.Johnson '<personname><firstname>Richard</firstname><othername>A.</othername><surname>Johnson</surname></personname>'>
<!ENTITY Richard.Johnson.mail '<email><EMAIL></email>'>
<!ENTITY Matt.Johnston '<personname><firstname>Matt</firstname><surname>Johnston</surname></personname>'>
<!ENTITY Matt.Johnston.mail '<email><EMAIL></email>'>
<!ENTITY Martin.R.Jones '<personname><firstname>Martin</firstname><othername>R.</othername><surname>Jones</surname></personname>'>
<!ENTITY Martin.R.Jones.mail '<email><EMAIL></email>'>

<!ENTITY Thomas.Kabelmann '<personname><firstname>Thomas</firstname><surname>Kabelmann</surname></personname>'>
<!ENTITY Thomas.Kabelmann.mail '<email><EMAIL></email>'>
<!ENTITY Sirtaj.Singh.Kang '<personname><firstname>Sirtaj</firstname><othername>Singh</othername><surname>Kang</surname></personname>'>
<!ENTITY Sirtaj.Singh.Kang.mail '<email><EMAIL></email>'>
<!ENTITY Rob.Kaper '<personname><firstname>Rob</firstname><surname>Kaper</surname></personname>'>
<!ENTITY Rob.Kaper.mail '<email><EMAIL></email>'>
<!ENTITY Jason.Katz-Brown '<personname><firstname>Jason</firstname><surname>Katz-Brown</surname></personname>'>
<!ENTITY Jason.Katz-Brown.mail '<email><EMAIL></email>'>
<!ENTITY Ulrich.Kuettler '<personname><firstname>Ulrich</firstname><surname>Küttler</surname></personname>'>
<!ENTITY Stefan.Kebekus '<personname><firstname>Stefan</firstname><surname>Kebekus</surname></personname>'>
<!ENTITY Stefan.Kebekus.mail '<email><EMAIL></email>'>
<!ENTITY Peter.Kelly '<personname><firstname>Peter</firstname><surname>Kelly</surname></personname>'>
<!ENTITY Peter.Kelly.mail '<email><EMAIL></email>'>
<!ENTITY Matthias.Kiefer '<personname><firstname>Matthias</firstname><surname>Kiefer</surname></personname>'>
<!ENTITY Matthias.Kiefer.mail '<email><EMAIL></email>'>
<!ENTITY Ingo.Kloecker '<personname><firstname>Ingo</firstname><surname>Klöcker</surname></personname>'>
<!ENTITY Ingo.Kloecker.mail '<email><EMAIL></email>'>
<!ENTITY Robert.Knight '<personname><firstname>Robert</firstname><surname>Knight</surname></personname>'>
<!ENTITY Robert.Knight.mail '<email><EMAIL></email>'>
<!ENTITY Lars.Knoll '<personname><firstname>Lars</firstname><surname>Knoll</surname></personname>'>
<!ENTITY Lars.Knoll.mail '<email><EMAIL></email>'>
<!ENTITY Michael.Koch '<personname><firstname>Michael</firstname><surname>Koch</surname></personname>'>
<!ENTITY Michael.Koch.mail '<email><EMAIL></email>'>
<!ENTITY Tobias.Koenig '<personname><firstname>Tobias</firstname><surname>Koenig</surname></personname>'>
<!ENTITY Tobias.Koenig.mail '<email><EMAIL></email>'>
<!ENTITY Michael.Korman '<personname><firstname>Michael</firstname><surname>Korman</surname></personname>'>
<!ENTITY Antti.Koivisto '<personname><firstname>Antti</firstname><surname>Koivisto</surname></personname>'>
<!ENTITY Antti.Koivisto.mail '<email><EMAIL></email>'>
<!ENTITY Martin.Koller '<personname><firstname>Martin</firstname><surname>Koller</surname></personname>'>
<!ENTITY Martin.Koller.mail '<email><EMAIL></email>'>
<!ENTITY Friedrich.Kossebau '<personname><firstname>Friedrich</firstname><othername>W. H.</othername><surname>Kossebau</surname></personname>'>
<!ENTITY Friedrich.Kossebau.email '<email><EMAIL></email>'>
<!ENTITY Matt.Koss '<personname><firstname>Matt</firstname><surname>Koss</surname></personname>'>
<!ENTITY Matt.Koss.mail '<email><EMAIL></email>'>
<!ENTITY Michael.Korman.mail '<email><EMAIL></email>'>
<!ENTITY Arnold.Kraschinski '<personname><firstname>Arnold</firstname><surname>Kraschinski</surname></personname>'>
<!ENTITY Arnold.Kraschinski.mail '<email><EMAIL></email>'>
<!ENTITY Michael.Kropfberger '<personname><firstname>Michael</firstname><surname>Kropfberger</surname></personname>'>
<!ENTITY Michael.Kropfberger.mail '<email><EMAIL></email>'>
<!ENTITY Ulrich.Kuettler.mail '<email><EMAIL></email>'>
<!ENTITY Sebastian.Kuegler '<personname><firstname>Sebastian</firstname><surname>Kügler</surname></personname>'>
<!ENTITY Sebastian.Kuegler.mail '<email><EMAIL></email>'>
<!ENTITY Stephan.Kulow '<personname><firstname>Stephan</firstname><surname>Kulow</surname></personname>'>
<!ENTITY Stephan.Kulow.mail '<email><EMAIL></email>'>
<!ENTITY Vladimir.Kuznetsov '<personname><firstname>Vladimir</firstname><surname>Kuznetsov</surname></personname>'>
<!ENTITY Vladimir.Kuznetsov.mail '<email><EMAIL></email>'>

<!ENTITY Raphael.Langerhorst '<personname><firstname>Raphael</firstname><surname>Langerhorst</surname></personname>'>
<!ENTITY Raphael.Langerhorst.mail '<email><EMAIL></email>'>
<!ENTITY Brian.C.Ledbetter '<personname><firstname>Brian</firstname><othername>C.</othername><surname>Ledbetter</surname></personname>'>
<!ENTITY Brian.C.Ledbetter.mail '<email><EMAIL></email>'>
<!ENTITY James.Lindenschmidt '<personname><firstname>James</firstname><surname>Lindenschmidt</surname></personname>'>
<!ENTITY James.Lindenschmidt.mail '<email><!-- FIXME --></email>'>
<!ENTITY Erwan.Loisant '<personname><firstname>Erwan</firstname><surname>Loisant</surname></personname>'>
<!ENTITY Erwan.Loisant.mail '<email><EMAIL></email>'>
<!ENTITY Neil.Lucock '<personname><firstname>Neil</firstname><surname>Lucock</surname></personname>'>
<!ENTITY Neil.Lucock.mail '<email><EMAIL></email>'>
<!ENTITY Burkhard.Lueck '<personname><firstname>Burkhard</firstname><surname>Lück</surname></personname>'>
<!ENTITY Burkhard.Lueck.mail '<email><EMAIL></email>'>
<!ENTITY Roger.Lum '<personname><firstname>Roger</firstname><surname>Lum</surname></personname>'>
<!ENTITY Roger.Lum.mail '<email><EMAIL></email>'>
<!ENTITY Anders.Lund '<personname><firstname>Anders</firstname><surname>Lund</surname></personname>'>
<!ENTITY Anders.Lund.mail '<email><EMAIL></email>'>
<!ENTITY Han.Young '<personname><firstname>Han</firstname><surname>Young</surname></personname>'>
<!ENTITY Han.Young.mail '<email><EMAIL></email>'>

<!ENTITY Anne-Marie.Mahfouf '<personname><firstname>Anne-Marie</firstname><surname>Mahfouf</surname></personname>'>
<!ENTITY Anne-Marie.Mahfouf.mail '<email><EMAIL></email>'>
<!ENTITY Martin.Maierhofer '<personname><firstname>Martin</firstname><surname>Maierhofer</surname></personname>'>
<!ENTITY Martin.Maierhofer.mail '<email><EMAIL></email>'>
<!ENTITY Marco.Martin '<personname><firstname>Marco</firstname><surname>Martin</surname></personname>'>
<!ENTITY Marco.Martin.mail '<email><EMAIL></email>'>
<!ENTITY Dirk.Mueller '<personname><firstname>Dirk</firstname><surname>Müller</surname></personname>'>
<!ENTITY Mike.McBride '<personname><firstname>Mike</firstname><surname>McBride</surname></personname>'>
<!ENTITY Mike.McBride.mail '<email>no mail</email>'>
<!ENTITY Robert.L.McCormick '<personname><firstname>Robert</firstname><othername>L.</othername><surname>McCormick</surname></personname>'>
<!ENTITY Robert.L.McCormick.mail '<email><EMAIL></email>'>
<!ENTITY Thad.McGinnis '<personname><firstname>Thad</firstname><surname>McGinnis</surname></personname>'>
<!ENTITY Thad.McGinnis.mail '<email><EMAIL></email>'>
<!ENTITY Roman.Merzlyakov '<personname><firstname>Roman</firstname><surname>Merzlyakov</surname></personname>'>
<!ENTITY Roman.Merzlyakov.mail '<email><EMAIL></email>'>
<!ENTITY Matthias.Messmer '<personname><firstname>Matthias</firstname><surname>Messmer</surname></personname>'>
<!ENTITY Matthias.Messmer.mail '<email><EMAIL></email>'>
<!ENTITY Gary.Meyer '<personname><firstname>Gary</firstname><surname>Meyer</surname></personname>'>
<!ENTITY Gary.Meyer.mail '<email><EMAIL></email>'>
<!ENTITY Klaus-Dieter.Moeller '<personname><firstname>Kaus-Dieter</firstname><surname>M&ouml;ller</surname></personname>'>
<!ENTITY Klaus-Dieter.Moeller.mail '<email><EMAIL></email>'>
<!ENTITY Daniel.Molkentin '<personname><firstname>Daniel</firstname><surname>Molkentin</surname></personname>'>
<!ENTITY Daniel.Molkentin.mail '<email><EMAIL></email>'>
<!ENTITY Mehrdad.Momeny '<personname><firstname>Mehrdad</firstname><surname>Momeny</surname></personname>'>
<!ENTITY Mehrdad.Momeny.mail '<email><EMAIL></email>'>
<!ENTITY Laurent.Montel '<personname><firstname>Laurent</firstname><surname>Montel</surname></personname>'>
<!ENTITY Laurent.Montel.mail '<email><EMAIL></email>'>
<!ENTITY Richard.J.Moore '<personname><firstname>Richard</firstname><othername>J.</othername><surname>Moore</surname></personname>'>
<!ENTITY Richard.J.Moore.mail '<email><EMAIL></email>'>
<!ENTITY Dirk.Mueller.mail '<email><EMAIL></email>'>
<!ENTITY Jasem.Mutlaq '<personname><firstname>Jasem</firstname><surname>Mutlaq</surname></personname>'>
<!ENTITY Jasem.Mutlaq.mail '<email><EMAIL></email>'>
<!ENTITY Marc.Mutz '<personname><firstname>Marc</firstname><surname>Mutz</surname></personname>'>
<!ENTITY Marc.Mutz.mail '<email><EMAIL></email>'>

<!ENTITY Daniel.Naber '<personname><firstname>Daniel</firstname><surname>Naber</surname></personname>'>
<!ENTITY Daniel.Naber.mail '<email><EMAIL></email>'>
<!ENTITY Armen.Nakashian '<personname><firstname>Armen</firstname><surname>Nakashian</surname></personname>'>
<!ENTITY Armen.Nakashian.mail '<email><EMAIL></email>'>
<!ENTITY Christoph.Neerfield '<personname><firstname>Christoph</firstname><surname>Neerfield</surname></personname>'>
<!ENTITY Christoph.Neerfield.mail '<email><EMAIL></email>'>
<!ENTITY Alexander.Neundorf '<personname><firstname>Alexander</firstname><surname>Neundorf</surname></personname>'>
<!ENTITY Alexander.Neundorf.mail '<email><EMAIL></email>'>
<!ENTITY Carsten.Niehaus '<personname><firstname>Carsten</firstname><surname>Niehaus</surname></personname>'>
<!ENTITY Carsten.Niehaus.mail '<email><EMAIL></email>'>
<!ENTITY Dennis.Nienhueser '<personname><firstname>Dennis</firstname><surname>Nienhüser</surname></personname>'>
<!ENTITY Dennis.Nienhueser.mail '<email><EMAIL></email>'>
<!ENTITY Golnaz.Nilieh '<personname><firstname>Golnaz</firstname><surname>Nilieh</surname></personname>'>
<!ENTITY Golnaz.Nilieh.mail '<email><EMAIL></email>'>
<!ENTITY Virgil.J.Nisly '<personname><firstname>Virgil</firstname><othername>J.</othername><surname>Nisly</surname></personname>'>
<!ENTITY Virgil.J.Nisly.mail '<email><EMAIL></email>'>

<!ENTITY Cristian.Onet '<personname><firstname>Cristian</firstname><surname>Oneț</surname></personname>'>
<!ENTITY Cristian.Onet.mail '<email><EMAIL></email>'>
<!ENTITY Jack.H.Ostroff '<personname><firstname>Jack</firstname><othername>H.</othername><surname>Ostroff</surname></personname>'>
<!ENTITY Jack.H.Ostroff.mail '<email><EMAIL></email>'>

<!ENTITY Maren.Pakura '<personname><firstname>Maren</firstname><surname>Pakura</surname></personname>'>
<!ENTITY Maren.Pakura.mail '<email><EMAIL></email>'>
<!ENTITY Randy.Pearson '<personname><firstname>Randy</firstname><surname>Pearson</surname></personname>'>
<!ENTITY Randy.Pearson.mail '<email><EMAIL></email>'>
<!ENTITY Jesper.Pedersen '<personname><firstname>Jesper</firstname><surname>Pedersen</surname></personname>'>
<!ENTITY Jesper.Pedersen.mail '<email><EMAIL></email>'>
<!ENTITY Carsten.Pfeiffer '<personname><firstname>Carsten</firstname><surname>Pfeiffer</surname></personname>'>
<!ENTITY Carsten.Pfeiffer.mail '<email><EMAIL></email>'>
<!ENTITY Kurt.Pfeifle '<personname><firstname>Kurt</firstname><surname>Pfeifle</surname></personname>'>
<!ENTITY Kurt.Pfeifle.mail '<email><EMAIL></email>'>
<!ENTITY Mauricio.Piacentini '<personname><firstname>Mauricio</firstname><surname>Piacentini</surname></personname>'>
<!ENTITY Mauricio.Piacentini.mail '<email><EMAIL></email>'>
<!ENTITY Dan.Pilone '<personname><firstname>Dan</firstname><surname>Pilone</surname></personname>'>
<!ENTITY Dan.Pilone.mail '<email><EMAIL></email>'>
<!ENTITY Clay.Pradarits '<personname><firstname>Clay</firstname><surname>Pradarits</surname></personname>'>
<!ENTITY Clay.Pradarits.mail '<email><EMAIL></email>'>
<!ENTITY Aleix.Pol '<personname><firstname>Aleix</firstname><surname>Pol</surname></personname>'>
<!ENTITY Aleix.Pol.mail '<email><EMAIL></email>'>
<!ENTITY Harri.Porten '<personname><firstname>Harri</firstname><surname>Porten</surname></personname>'>
<!ENTITY Harri.Porten.mail '<email><EMAIL></email>'>
<!ENTITY Milos.Prudek '<personname><firstname>Milos</firstname><surname>Prudek</surname></personname>'>
<!ENTITY Milos.Prudek.mail '<email><EMAIL></email>'>
<!ENTITY Michael.Pyne '<personname><firstname>Michael</firstname><surname>Pyne</surname></personname>'>
<!ENTITY Michael.Pyne.mail '<email><EMAIL></email>'>

<!ENTITY Frerich.Raabe '<personname><firstname>Frerich</firstname><surname>Raabe</surname></personname>'>
<!ENTITY Frerich.Raabe.mail '<email><EMAIL></email>'>
<!ENTITY Torsten.Rahn '<personname><firstname>Torsten</firstname><surname>Rahn</surname></personname>'>
<!ENTITY Torsten.Rahn.mail '<email><EMAIL></email>'>
<!ENTITY Ravikiran.Rajagopal '<personname><firstname>Ravikiran</firstname><surname>Rajagopal</surname></personname>'>
<!ENTITY Ravikiran.Rajagopal.mail '<email><EMAIL></email>'>
<!ENTITY Artur.Rataj '<personname><firstname>Artur</firstname><surname>Rataj</surname></personname>'>
<!ENTITY Artur.Rataj.mail '<email><EMAIL></email>'>
<!ENTITY Pablo.Rauzy '<firstname>Pablo</firstname> <surname>Rauzy</surname>'>
<!ENTITY Pablo.Rauzy.mail '<email>r .at. uzy.me</email>'>
<!ENTITY Roman.Razilov '<personname><firstname>Roman</firstname><surname>Razilov</surname></personname>'>
<!ENTITY Roman.Razilov.mail '<email><EMAIL></email>'>
<!ENTITY Michael.Reiher '<personname><firstname>Michael</firstname><surname>Reiher</surname></personname>'>
<!ENTITY Michael.Reiher.mail '<email><EMAIL></email>'>
<!ENTITY Alexander.Reinholdt '<firstname>Alexander</firstname><surname>Reinholdt</surname></personname>'>
<!ENTITY Alexander.Reinholdt.mail '<email><EMAIL></email>'>
<!ENTITY Alex.Richardson '<personname><firstname>Alex</firstname><surname>Richardson</surname></personname>'>
<!ENTITY Alex.Richardson.email '<email><EMAIL></email>'>
<!ENTITY Wolfgang.Rohdewald '<personname><firstname>Wolfgang</firstname><surname>Rohdewald</surname></personname>'>
<!ENTITY Wolfgang.Rohdewald.mail '<email><EMAIL></email>'>
<!ENTITY Nicholas.Robbins '<personname><firstname>Nicolas</firstname><surname>Robbins</surname></personname>'>
<!ENTITY Nicholas.Robbins.mail '<email><EMAIL></email>'>
<!ENTITY Pamela.Roberts '<personname><firstname>Pamela</firstname><surname>Roberts</surname></personname>'>
<!ENTITY Pamela.Roberts.mail '<email><EMAIL></email>'>
<!ENTITY Hamish.Rodda '<personname><firstname>Hamish</firstname><surname>Rodda</surname></personname>'>
<!ENTITY Hamish.Rodda.mail '<email><EMAIL></email>'>
<!ENTITY Philip.Rodrigues '<personname><firstname>Philip</firstname><surname>Rodrigues</surname></personname>'>
<!ENTITY Philip.Rodrigues.mail '<email><EMAIL></email>'>
<!ENTITY Nicolas.Roffet '<personname><firstname>Nicolas</firstname><surname>Roffet</surname></personname>'>
<!ENTITY Nicolas.Roffet.mail '<email><EMAIL></email>'>
<!ENTITY Seth.Rothberg '<personname><firstname>Seth</firstname><surname>Rothberg</surname></personname>'>
<!ENTITY Seth.Rothberg.mail '<email><EMAIL></email>'>
<!ENTITY David.Rugge '<personname><firstname>David</firstname><surname>Rugge</surname></personname>'>
<!ENTITY David.Rugge.mail '<email><EMAIL></email>'>
<!ENTITY Andriy.Rysin '<personname><firstname>Andriy</firstname><surname>Rysin</surname></personname>'>
<!ENTITY Andriy.Rysin.mail '<email><EMAIL></email>'>
<!ENTITY Teemu.Rytilahti '<personname><firstname>Teemu</firstname><surname>Rytilahti</surname></personname>'>
<!ENTITY Teemu.Rytilahti.mail '<email><EMAIL></email>'>

<!ENTITY Charles.Samuels '<personname><firstname>Charles</firstname><surname>Samuels</surname></personname>'>
<!ENTITY Charles.Samuels.mail '<email><EMAIL></email>'>
<!ENTITY Espen.Sand '<personname><firstname>Espen</firstname><surname>Sand</surname></personname>'>
<!ENTITY Espen.Sand.mail '<email><EMAIL></email>'>
<!ENTITY Don.Sanders '<personname><firstname>Don</firstname><surname>Sanders</surname></personname>'>
<!ENTITY Don.Sanders.mail '<email><EMAIL></email>'>
<!ENTITY Morgan.N.Sandquist '<personname><firstname>Morgan</firstname><othername>N.</othername><surname>Sandquist</surname></personname>'>
<!ENTITY Morgan.N.Sandquist.mail '<email><EMAIL></email>'>
<!ENTITY Raffaele.Sandrini '<personname><firstname>Raffaele</firstname><surname>Sandrini</surname></personname>'>
<!ENTITY Olivier.Saraja '<personname><firstname>Olivier</firstname><surname>Saraja</surname></personname>'>
<!ENTITY Olivier.Saraja.mail '<email><EMAIL></email>'>
<!ENTITY Thomas.Schuetz '<personname><firstname>Thomas</firstname><surname>Schütz</surname></personname>'>
<!ENTITY Jost.Schenck '<personname><firstname>Jost</firstname><surname>Schenck</surname></personname>'>
<!ENTITY Jost.Schenck.mail '<email><EMAIL></email>'>
<!ENTITY Chris.Schlaeger '<personname><firstname>Chris</firstname><surname>Schlaeger</surname></personname>'>
<!ENTITY Chris.Schlaeger.mail '<email><EMAIL></email>'>
<!ENTITY Thomas.Schuetz.mail '<email><EMAIL></email>'>
<!ENTITY Cornelius.Schumacher '<personname><firstname>Cornelius</firstname><surname>Schumacher</surname></personname>'>
<!ENTITY Cornelius.Schumacher.mail '<email><EMAIL></email>'>
<!ENTITY Guenter.Schwann '<personname><firstname>Günter</firstname><surname>Schwann</surname></personname>'>
<!ENTITY Guenter.Schwann.mail '<email><EMAIL></email>'>
<!ENTITY Frederik.Schwarzer '<personname><firstname>Frederik</firstname><surname>Schwarzer</surname></personname>'>
<!ENTITY Frederik.Schwarzer.mail '<email><EMAIL></email>'>
<!ENTITY Aaron.J.Seigo '<personname><firstname>Aaron</firstname><othername>J.</othername><surname>Seigo</surname></personname>'>
<!ENTITY Aaron.J.Seigo.mail '<email><EMAIL></email>'>
<!ENTITY Jonathan.Singer '<personname><firstname>Jonathan</firstname><surname>Singer</surname></personname>'>
<!ENTITY Jonathan.Singer.mail '<email><EMAIL></email>'>
<!ENTITY Alvaro.Soliverez '<personname><firstname>Alvaro</firstname><surname>Soliverez</surname></personname>'>
<!ENTITY Alvaro.Soliverez.mail '<email><EMAIL></email>'>
<!ENTITY Martin.Sommer '<personname><firstname>Martin</firstname><surname>Sommer</surname></personname>'>
<!ENTITY Martin.Sommer.mail '<email><EMAIL></email>'>
<!ENTITY Klaus.Staerk '<personname><firstname>Klaus</firstname><surname>Stärk</surname></personname>'>
<!ENTITY Klaus.Staerk.mail '<email><EMAIL></email>'>
<!ENTITY Andrew.Stanley-Jones '<personname><firstname>Andrew</firstname><surname>Stanley-Jones</surname></personname>'>
<!ENTITY Andrew.Stanley-Jones.mail '<email><EMAIL></email>'>
<!ENTITY George.Staikos '<personname><firstname>George</firstname><surname>Staikos</surname></personname>'>
<!ENTITY George.Staikos.mail '<email><EMAIL></email>'>
<!ENTITY Russ.Steffen '<personname><firstname>Russ</firstname><surname>Steffen</surname></personname>'>
<!ENTITY Russ.Steffen.mail '<email><EMAIL></email>'>
<!ENTITY Neil.Stevens '<personname><firstname>Neil</firstname><surname>Stevens</surname></personname>'>
<!ENTITY Neil.Stevens.mail '<email><EMAIL></email>'>
<!ENTITY Darin.Strait '<personname><firstname>Darin</firstname><surname>Strait</surname></personname> '>
<!ENTITY Darin.Strait.mail '<email><EMAIL></email>'>
<!ENTITY David.Sweet '<personname><firstname>David</firstname><surname>Sweet</surname></personname>'>
<!ENTITY David.Sweet.mail '<email><EMAIL></email>'>

<!ENTITY Stefan.Taferner '<personname><firstname>Stefan</firstname><surname>Taferner</surname></personname>'>
<!ENTITY Stefan.Taferner.mail '<email><EMAIL></email>'>
<!ENTITY Mark.Taff '<personname><firstname>Mark</firstname><surname>Taff</surname></personname>'>
<!ENTITY Mark.Taff.mail '<email><EMAIL></email>'>
<!ENTITY Thomas.Tanghus '<personname><firstname>Thomas</firstname><surname>Tanghus</surname></personname>'>
<!ENTITY Thomas.Tanghus.mail '<email><EMAIL></email>'>
<!ENTITY John.Tapsell '<personname><firstname>John</firstname><surname>Tapsell</surname></personname>'>
<!ENTITY John.Tapsell.mail '<email><EMAIL></email>'>
<!ENTITY Krishna.Tateneni '<personname><firstname>Krishna</firstname><surname>Tateneni</surname></personname>'>
<!ENTITY Krishna.Tateneni.mail '<email><EMAIL></email>'>
<!ENTITY Roberto.Teixeira '<personname><firstname>Roberto</firstname><surname>Teixeira</surname></personname>'>
<!ENTITY Roberto.Teixeira.mail '<email><EMAIL></email>'>
<!ENTITY Evan.Teran '<personname><firstname>Evan</firstname><surname>Teran</surname></personname>'>
<!ENTITY Evan.Teran.mail '<email><EMAIL></email>'>
<!ENTITY Phil.Thompson '<personname><firstname>Phil</firstname><surname>Thompson</surname></personname>'>
<!ENTITY Phil.Thompson.mail '<email><EMAIL></email>'>
<!ENTITY Ragnar.Thompson '<personname><firstname>Ragnar</firstname><surname>Thompson</surname></personname>'>
<!ENTITY Ragnar.Thompson.mail '<email><EMAIL></email>'>
<!ENTITY Bo.Thorsen '<personname><firstname>Bo</firstname><surname>Thorsen</surname></personname>'>
<!ENTITY Bo.Thorsen.mail '<email><EMAIL></email>'>
<!ENTITY Christian.Thurner '<personname><firstname>Christian</firstname><surname>Thurner</surname></personname>'>
<!ENTITY Christian.Thurner.mail '<email><EMAIL></email>'>
<!ENTITY Cristian.Tibirna '<personname><firstname>Christian</firstname><surname>Tibirna</surname></personname>'>
<!ENTITY Cristian.Tibirna.mail '<email><EMAIL></email>'>
<!ENTITY Jochen.Tuchbreiter '<personname><firstname>Jochen</firstname><surname>Tuchbreiter</surname></personname>'>
<!ENTITY Jochen.Tuchbreiter.mail '<email><EMAIL></email>'>
<!ENTITY Paul-Olav.Tvete '<personname><firstname>Paul Olav</firstname><surname>Tvete</surname></personname>'>
<!ENTITY Paul-Olav.Tvete.mail '<email><!-- FIXME --></email>'>

<!ENTITY Jan-Pascal.vanBest '<personname><firstname>Jan-Pascal</firstname><surname>van Best</surname></personname>'>
<!ENTITY Jan-Pascal.vanBest.mail '<email><EMAIL></email>'>
<!ENTITY Stas.Verberkt '<personname><firstname>Stas</firstname><surname>Verberkt</surname></personname>'>
<!ENTITY Stas.Verberkt.mail '<email><EMAIL></email>'>
<!ENTITY Fernando.Vilas '<personname><firstname>Fernando</firstname><surname>Vilas</surname></personname>'>
<!ENTITY Fernando.Vilas.mail '<email><EMAIL></email>'>
<!ENTITY Stanislav.Visnovsky '<personname><firstname>Stanislav</firstname><surname>Visnovsky</surname></personname>'>
<!ENTITY Stanislav.Visnovsky.mail '<email><EMAIL></email>'>
<!ENTITY Sean.Vyain '<personname><firstname>Sean</firstname><surname>Vyain</surname></personname>'>
<!ENTITY Sean.Vyain.mail '<email><EMAIL></email>'>

<!ENTITY Mathias.Waack '<personname><firstname>Mathias</firstname><surname>Waack</surname></personname>'>
<!ENTITY Mathias.Waack.mail '<email><EMAIL></email>'>
<!ENTITY Robert.Wadley '<personname><firstname>Robert</firstname><surname>Wadley</surname></personname>'>
<!ENTITY Robert.Wadley.mail '<email><EMAIL></email>'>
<!ENTITY Joerg.Walter '<personname><firstname>J&ouml;rg</firstname><surname>Walter</surname></personname>'>
<!ENTITY Joerg.Walter.mail '<email><EMAIL></email>'>
<!ENTITY Lauri.Watts '<personname><firstname>Lauri</firstname><surname>Watts</surname></personname>'>
<!ENTITY Lauri.Watts.mail '<email><EMAIL></email>'>
<!ENTITY Josef.Weidendorfer '<personname><firstname>Josef</firstname><surname>Weidendorfer</surname></personname>'>
<!ENTITY Josef.Weidendorfer.mail '<email><EMAIL></email>'>
<!ENTITY Mario.Weilguni '<personname><firstname>Mario</firstname><surname>Weilguni</surname></personname>'>
<!ENTITY Mario.Weilguni.mail '<email><EMAIL></email>'>
<!ENTITY Mattias.Welk '<personname><firstname>Mattias</firstname><surname>Welk</surname></personname>'>
<!ENTITY Mattias.Welk.mail '<email><EMAIL></email>'>
<!ENTITY Joseph.Wenninger '<personname><firstname>Joseph</firstname><surname>Wenninger</surname></personname>'>
<!ENTITY Joseph.Wenninger.mail '<email><EMAIL></email>'>
<!ENTITY Scott.Wheeler '<personname><firstname>Scott</firstname><surname>Wheeler</surname></personname>'>
<!ENTITY Scott.Wheeler.mail '<email><EMAIL></email>'>
<!ENTITY Anders.Widell '<personname><firstname>Anders</firstname><surname>Widell</surname></personname>'>
<!ENTITY Anders.Widell.mail '<email><EMAIL></email>'>
<!ENTITY Matthew.Woehlke '<personname><firstname>Matthew</firstname><surname>Woehlke</surname></personname>'>
<!ENTITY Matthew.Woehlke.mail '<email><EMAIL></email>'>
<!ENTITY Marc.Wolf '<personname><firstname>Marc</firstname><surname>Wolf</surname></personname>'>
<!ENTITY Marc.Wolf.mail '<email><EMAIL></email>'>
<!ENTITY Colin.Wright '<personname><firstname>Colin</firstname><surname>Wright</surname></personname>'>
<!ENTITY Colin.Wright.mail '<email><EMAIL></email>'>
<!ENTITY Bernd.Johannes.Wuebben '<personname><firstname>Bernd</firstname><othername>Johannes</othername><surname>Wuebben</surname></personname>'>
<!ENTITY Bernd.Johannes.Wuebben.mail '<email><EMAIL></email>'>

<!ENTITY Thorsten.Zachmann '<personname><firstname>Thorsten</firstname><surname>Zachmann</surname></personname>'>
<!ENTITY Thorsten.Zachmann.mail '<email><EMAIL></email>'>
<!ENTITY Andreas.Zehender '<personname><firstname>Andreas</firstname><surname>Zehender</surname></personname>'>
<!ENTITY Andreas.Zehender.mail '<email><EMAIL></email>'>
<!ENTITY Alex.Zepeda '<personname><firstname>Alex</firstname><surname>Zepeda</surname></personname>'>
<!ENTITY Alex.Zepeda.mail '<email><EMAIL></email>'>
<!ENTITY Nikolas.Zimmermann '<personname><firstname>Nikolas</firstname><surname>Zimmermann</surname></personname>'>
<!ENTITY Nikolas.Zimmermann.mail '<email><EMAIL></email>'>
<!ENTITY Jakob.Petsovits '<personname><firstname>Jakob</firstname><surname>Petsovits</surname></personname>'>
<!ENTITY Jakob.Petsovits.mail '<email><EMAIL></email>'>
