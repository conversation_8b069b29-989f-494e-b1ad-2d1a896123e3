<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.vaguedenoiser" id="avfilter.vaguedenoiser">
    <name>Wavelet Denoiser</name>
    <description>Wavelet based Denoiser</description>
    <author>libavfilter</author>
    <parameter type="list" name="av.method" default="soft" paramlist="hard;soft;garrote">
        <paramlistdisplay>Hard,Soft,Garrote</paramlistdisplay>
        <name>Method</name>
    </parameter>
    <parameter type="constant" name="av.threshold" default="2" min="0" max="25000" factor="1">
        <name>Threshold</name>
    </parameter>
    <parameter type="constant" name="av.nsteps" default="6" min="1" max="32" factor="1">
        <name>Steps</name>
    </parameter>
    <parameter type="constant" name="av.percent" default="0" min="0" max="100" factor="1" suffix="%">
        <name>Percentage</name>
    </parameter>
    <parameter type="list" name="av.planes" default="7" paramlist="0;1;2;3;4;5;6;7;8">
        <paramlistdisplay>None,Y,U,YU,V,YV,UV,YUV,Alpha</paramlistdisplay>
        <name>Planes</name>
    </parameter>
</effect>
