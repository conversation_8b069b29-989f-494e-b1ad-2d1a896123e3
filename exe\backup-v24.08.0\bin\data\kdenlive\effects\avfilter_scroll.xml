<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.scroll" id="avfilter.scroll">
    <name>Scroll</name>
    <description>Pick median pixel from certain rectangle defined by radius.</description>
    <author>libavfilter</author>
    <parameter type="animated" name="av.h" min="-1" max="1" default="0" decimals="3">
        <name>Horizontal scrolling speed</name>
    </parameter>
    <parameter type="animated" name="av.v" min="-1" max="1" default="0" decimals="3">
        <name>Vertical scrolling speed</name>
    </parameter>
    <parameter type="animated" name="av.hpos" min="0" max="1" default="0" decimals="3">
        <name>Initial horizontal position</name>
    </parameter>
    <parameter type="animated" name="av.vpos" min="0" max="1" default="0" decimals="3">
        <name>Initial vertical position</name>
    </parameter>
</effect>
