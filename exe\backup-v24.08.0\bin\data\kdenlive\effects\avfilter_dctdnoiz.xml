<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.dctdnoiz" id="avfilter.dctdnoiz">
    <name>DCT Denoiser</name>
    <description>Denoise frames using 2D DCT frequency domain filtering</description>
    <author>libavfilter</author>
    <parameter type="constant" name="av.s" default="0" max="999" min="0" decimals="3">
        <name>Sigma</name>
    </parameter>
    <parameter type="constant" name="av.overlap" default="0" max="7" min="0" factor="1">
        <name>Overlap</name>
    </parameter>
    <parameter type="switch" name="av.n" default="3" max="4" min="3">
        <name>Block size</name>
    </parameter>
</effect>
