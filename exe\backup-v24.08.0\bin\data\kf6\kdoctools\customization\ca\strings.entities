<!-- These entities should be translated, but NOT CHANGED, NOR EXTENDED.
     For language-specific extensions, use user.entities.
     Translate everything between quotes, except names of general
     entities (&...;). -->

<!ENTITY kappname "aquesta aplicació">
<!-- Entities to fill in slots in docbook version of FDL notice.
     The default values of the parameter entities is IGNORE. -->
<![%FDLIS;[
<!ENTITY FDLISTitles "EL SEU LLISTAT DE TÍTOLS"><!-- keep capitals -->
<!ENTITY FDLInvariantSections "les seccions invariants estan &FDLISTitles;">
 ]]>
<!ENTITY FDLInvariantSections "seccions no variants">
<![%FDLFCT;[
<!ENTITY FDLFCTTitles "LLISTAT"><!-- keep capitals -->
<!ENTITY FDLFrontCoverText "el text de la portada està &FDLFCTTitles;">
 ]]>
<!ENTITY FDLFrontCoverText "sense text a la portada">
<![%FDLBCT;[
<!ENTITY FDLBCTTitles "ELS SEUS TÍTOLS LLISTATS"><!-- keep capitals -->
<!ENTITY FDLBackCoverText "el text de la contraportada està &FDLBCTTitles;">
 ]]>
<!ENTITY FDLBackCoverText "sense text a la contraportada">

<!-- modespec entity: must be adapted in accordance with the normal usage
     for documents in a language; the most likely candidates are the value
     of xreflabel (now %t for title of section referred to) and the content
     (now empty).  If more than one format is needed, contact <EMAIL>.
     ** In general, this setup will not work with more than one language in 
        a document **
     Usage: in <bookinfo>
     Only strictly needed when olinks are used
 -->
<!--ENTITY kde-modespec '<modespec id="kdems-default" xreflabel="&percnt;t"></modespec>'-->
<!ENTITY kde.modespec '
 <modespec id="kdems-help">help:</modespec>
 <modespec id="kdems-man">man:</modespec>'>

<!ENTITY olinktype "kde-installation">
