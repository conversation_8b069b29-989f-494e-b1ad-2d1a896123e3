[Desktop Entry]
Type=Application
Exec=kde-geo-uri-handler --coordinate-template "https://wheelmap.org/?lat=<LAT>&lon=<LON>" --query-template "https://wheelmap.org/search?q=<Q>" --fallback "https://wheelmap.org" %u
Name=wheelmap.org
Name[ar]=wheelmap.org
Name[ast]=wheelmap.org
Name[bg]=wheelmap.org
Name[ca]=wheelmap.org
Name[ca@valencia]=wheelmap.org
Name[cs]=wheelmap.org
Name[de]=wheelmap.org
Name[en_GB]=wheelmap.org
Name[eo]=wheelmap.org
Name[es]=wheelmap.org
Name[eu]=wheelmap.org
Name[fi]=wheelmap.org
Name[fr]=wheelmap.org
Name[gl]=wheelmap.org
Name[he]=wheelmap.org
Name[hi]=व्हीलमैप्स.ऑर्ग
Name[hu]=wheelmap.org
Name[ia]=wheelmap.org
Name[is]=wheelmap.org
Name[it]=wheelmap.org
Name[ka]=wheelmap.org
Name[ko]=wheelmap.org
Name[lt]=wheelmap.org
Name[lv]=wheelmap.org
Name[nl]=wheelmap.org
Name[nn]=wheelmap.org
Name[pl]=wheelmap.org
Name[pt]=wheelmap.org
Name[pt_BR]=wheelmap.org
Name[ro]=wheelmap.org
Name[ru]=wheelmap.org
Name[sa]=wheelmap.org
Name[sk]=wheelmap.org
Name[sl]=wheelmap.org
Name[sv]=wheelmap.org
Name[tr]=wheelmap.org
Name[uk]=wheelmap.org
Name[vi]=wheelmap.org
Name[x-test]=xxwheelmap.orgxx
Name[zh_CN]=wheelmap.org
Name[zh_TW]=wheelmap.org
Icon=map-globe
MimeType=x-scheme-handler/geo;
Terminal=false
NoDisplay=true
