<!-- These entities should be translated, but NOT CHANGED, NOR EXTENDED.
     For language-specific extensions, use user.entities.
     Translate everything between quotes, except names of general 
     entities (&...;). -->

<!ENTITY kappname "questa applicazione">
<!-- Entities to fill in slots in docbook version of FDL notice.
     The default values of the parameter entities is IGNORE. -->
<![%FDLIS;[
<!ENTITY FDLISTitles "LISTA DEI LORO TITOLI"><!-- keep capitals -->
<!ENTITY FDLInvariantSections "con le sezioni non modificabili &FDLISTitles;">
 ]]>
<!ENTITY FDLInvariantSections "senza sezioni non modificabili">
<![%FDLFCT;[
<!ENTITY FDLFCTTitles "LISTA"><!-- keep capitals -->
<!ENTITY FDLFrontCoverText "con i testi di copertina &FDLFCTTitles;">
 ]]>
<!ENTITY FDLFrontCoverText "senza testi di copertina">
<![%FDLBCT;[
<!ENTITY FDLBCTTitles "LISTA DEI LORO TITOLI"><!-- keep capitals -->
<!ENTITY FDLBackCoverText "con i testi di quarta di copertina &FDLBCTTitles;">
 ]]>
<!ENTITY FDLBackCoverText "senza testo di quarta di copertina">

<!-- modespec entity: must be adapted in accordance with the normal usage
     for documents in a language; the most likely candidates are the value
     of xreflabel (now %t for title of section referred to) and the content
     (now empty).  If more than one format is needed, contact <EMAIL>.
     ** In general, this setup will not work with more than one language in 
        a document **
     Usage: in <bookinfo>
     Only strictly needed when olinks are used
 -->
<!--ENTITY kde-modespec '<modespec id="kdems-default" xreflabel="&percnt;t"></modespec>'-->
<!ENTITY kde.modespec '
 <modespec id="kdems-help">help:</modespec>
 <modespec id="kdems-man">man:</modespec>'>

<!ENTITY olinktype "kde-installation">
