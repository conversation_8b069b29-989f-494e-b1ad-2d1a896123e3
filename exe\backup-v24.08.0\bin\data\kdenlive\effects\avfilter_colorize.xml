<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.colorize" id="avfilter.colorize">
    <name>Color Overlay</name>
    <description>Overlay a solid color on the video stream with mixing feature.</description>
    <author>libavfilter</author>
    <parameter type="animated" name="av.hue" default="0" min="0" max="360" decimals="3">
        <name> Hue</name>
    </parameter>
    <parameter type="animated" name="av.saturation" default="0.5" min="0" max="1" decimals="3">
        <name>Saturation</name>
    </parameter>
    <parameter type="animated" name="av.lightness" default="0.5" min="0" max="1" decimals="3">
        <name>Lightness</name>
    </parameter>
    <parameter type="animated" name="av.mix" default="1" min="0" max="1" decimals="3">
        <name>Mix</name>
    </parameter>
</effect>
