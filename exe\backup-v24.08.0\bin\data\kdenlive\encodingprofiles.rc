[decklink]
# timeline preview settings should be MLT type parameters, like: vcodec=libx264
x264=crf=25 ab=192k vcodec=libx264 acodec=libvorbis ab=192k preset=veryfast threads=%threads;mov
DNxHD=vcodec=dnxhd vb=145000k acodec=pcm_s16le threads=%threads;mov
MPEG=qscale=4 ab=192k vcodec=mpeg2video acodec=mp2 threads=%threads;mpg

[proxy]
# proxy settings should be FFmpeg type parameters, like: -vcodec libx264
x264=-vf scale=%width:-2 -vcodec libx264 -g 1 -bf 0 -vb 0 -crf 20 -preset veryfast -acodec aac -ab 128k;mov
x264-vaapi=-init_hw_device vaapi=vaapi0: -filter_hw_device vaapi0 -i -vf scale=%width:-2,format=nv12,hwupload -codec:v h264_vaapi -g 1 -bf 0 -qp 26 -acodec ac3 -ab 128k;mov
x264-amf=-f mp4 -codec:v h264_amf -g 1 -bf 0 -qp 26 -acodec ac3 -ab 128k;mp4
x264-qsv=-f mp4 -codec:v h264_qsv -g 1 -bf 0 -qp 26 -acodec ac3 -ab 128k;mp4
x264-videotoolbox=-f mp4 -codec:v h264_videotoolbox -g 1 -bf 0 -qp 26 -acodec ac3 -ab 128k;mp4
x264-vaapi-scale=-hwaccel vaapi -hwaccel_output_format vaapi -i -vf scale_vaapi=w=%width:h=-2:format=nv12,hwupload -codec:v h264_vaapi -g 1 -bf 0 -qp 26 -acodec ac3 -ab 128k;mov
x264-nvenc=-vsync 0 -c:v %nvcodec -resize %frameSize -i -vcodec h264_nvenc -g 2 -bf 0 -acodec copy;mov
MPEG2=-vf scale=%width:-2 -g 1 -bf 0 -vb 0 -qscale 6 -ab 128k -vcodec mpeg2video -acodec ac3;mpg
MJPEG=-vf yadif,scale=%width:-2 -qscale 3 -vcodec mjpeg -acodec pcm_s16le;mkv
MJPEG-vaapi=-init_hw_device vaapi=vaapi0:,connection_type=x11 -filter_hw_device vaapi0 -vf format=nv12,hwupload -codec:v mjpeg_vaapi -codec:a copy;mkv
ProRes=-vf scale=%width:-2 -vcodec prores_ks -vb 0 -g 1 -bf 0 -vprofile 0 -vendor ap10 -qscale 11;mov
NVENC H264=-vcodec h264_nvenc -vb 30000k -rc cbr -acodec aac -ab 192k;mp4
NVENC H265=-vcodec hevc_nvenc -vb 30000k -acodec aac -ab 192k;mp4

[screengrab]
# proxy settings should be FFmpeg type parameters, like: -vcodec libx264
X264 mute=-crf 25 -vcodec libx264 -preset veryfast -threads 0;mov
X264 with audio (alsa)=-f alsa -i default -crf 25 -ab 192k -vcodec libx264 -acodec libvorbis -preset veryfast -threads 0;mov
X264 with audio (pulse)=-f pulse -i default -crf 25 -ab 192k -vcodec libx264 -preset veryfast -threads 0;mov

[video4linux]
x264=crf=25 ab=192k vcodec=libx264 acodec=libvorbis ab=192k preset=veryfast threads=%threads;mov
MPEG=qscale=4 ab=192k vcodec=mpeg2video acodec=mp2 threads=%threads;mpg

[timelinepreview]
# timeline preview settings should be MLT type parameters, like: vcodec=libx264
DNxHD 1080p 23.976fps=r=23.976 s=1920x1080 vb=36M threads=0 vcodec=dnxhd progressive=1;mov
DNxHD 1080p 24fps=r=24 s=1920x1080 vb=36M threads=0 vcodec=dnxhd progressive=1;mov
DNxHD 1080p 25fps=r=25 s=1920x1080 vb=36M threads=0 vcodec=dnxhd progressive=1;mov
DNxHD 1080p 29.97fps=r=29.97 s=1920x1080 vb=45M threads=0 vcodec=dnxhd progressive=1;mov
DNxHD 1080p 30fps=r=30 s=1920x1080 vb=45M threads=0 vcodec=dnxhd progressive=1;mov
DNxHD 1080p 50fps=r=50 s=1920x1080 vb=75M threads=0 vcodec=dnxhd progressive=1;mov
DNxHD 1080p 59.94fps=r=59.94 s=1920x1080 vb=90M threads=0 vcodec=dnxhd progressive=1;mov
DNxHD 1080p 60fps=r=60 s=1920x1080 vb=90M threads=0 vcodec=dnxhd progressive=1;mov
ProRes=vcodec=prores_ks vb=0 g=1 bf=0 vprofile=0 vendor=ap10 qscale=4 s=800x450;mov
MJPEG=f=avi vcodec=mjpeg progressive=1 qscale=1;avi
x264-nvenc=c:v=h264_nvenc g=0 preset=fast qmin=10 qmax=30;mkv
x264-vaapi=c:v=h264_vaapi g=1 bf=0 vprofile=main preset=fast;mp4
x264-amf=c:v=h264_amf g=1 bf=0 vprofile=main preset=fast;mp4
x264-qsv=c:v=h264_qsv g=1 bf=0 vprofile=main preset=fast;mp4
h264-videotoolbox=c:v=h264_videotoolbox g=1 bf=0 vprofile=main preset=fast;mp4
NVENC H264=c:v=h264_nvenc vb=0 rc=cbr;mp4
NVENC H265=c:v=hevc_nvenc vb=0;mp4
VAAPI Intel=vaapi_device=/dev/dri/renderD128 vf=’format=nv12,hwupload’ vcodec=h264_vaapi vb=30000k;mp4
VAAPI AMD=hwaccel=vaapi hwaccel_device=renderD129 hwaccel_output_format=vaapi vcodec=h264_vaapi vb=30000k;mp4
