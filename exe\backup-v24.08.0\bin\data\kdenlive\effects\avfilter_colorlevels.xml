<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.colorlevels" id="avfilter.colorlevels">
    <name>Color levels</name>
    <description>Adjust video input frames using levels. </description>
    <author>libavfilter</author>
    <parameter type="constant" name="av.rimin" default="0" min="0" max="1" decimals="2">
        <name>Red black input</name>
    </parameter>
    <parameter type="constant" name="av.rimax" default="1" min="0" max="1" decimals="2">
        <name>Red white input</name>
    </parameter>
    <parameter type="constant" name="av.romin" default="0" min="0" max="1" decimals="2">
        <name>Red black output</name>
    </parameter>
    <parameter type="constant" name="av.romax" default="1" min="0" max="1" decimals="2">
        <name>Red white output</name>
    </parameter>
    <parameter type="constant" name="av.gimin" default="0" min="0" max="1" decimals="2">
        <name>Green black input</name>
    </parameter>
    <parameter type="constant" name="av.gimax" default="1" min="0" max="1" decimals="2">
        <name>Green white input</name>
    </parameter>
    <parameter type="constant" name="av.gomin" default="0" min="0" max="1" decimals="2">
        <name>Green black output</name>
    </parameter>
    <parameter type="constant" name="av.gomax" default="1" min="0" max="1" decimals="2">
        <name>Green white output</name>
    </parameter>
    <parameter type="constant" name="av.bimin" default="0" min="0" max="1" decimals="2">
        <name>Blue black input</name>
    </parameter>
    <parameter type="constant" name="av.bimax" default="1" min="0" max="1" decimals="2">
        <name>Blue white input</name>
    </parameter>
    <parameter type="constant" name="av.bomin" default="0" min="0" max="1" decimals="2">
        <name>Blue black output</name>
    </parameter>
    <parameter type="constant" name="av.bomax" default="1" min="0" max="1" decimals="2">
        <name>Blue white output</name>
    </parameter>
</effect>
