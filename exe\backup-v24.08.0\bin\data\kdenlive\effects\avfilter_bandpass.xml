<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.bandpass" id="avfilter.bandpass" type="audio">
    <name>Band-pass</name>
    <description>Apply a two-pole Butterworth band-pass filter with central frequency, and (3dB-point) band-width width. The csg option selects a constant skirt gain (peak gain = Q) instead of the default: constant 0dB peak gain. The filter roll off at 6dB per octave (20dB per decade). </description>
    <author>libavfilter</author>
    <parameter type="constant" name="av.f" default="3000" min="20" max="20000" suffix="Hz">
        <name>Central Frequency</name>
    </parameter>
    <parameter type="bool" name="av.csg" default="0">
        <name>Constant skirt gain</name>
    </parameter>
    <parameter type="list" name="av.t" default="h" paramlist="h;q;o;s;k">
        <paramlistdisplay>Hz,Q-FActor,Octave,Slope,KHz</paramlistdisplay>
        <name>Method</name>
    </parameter>
    <parameter type="constant" name="av.w" default="0.5" min="1" max="9999" decimals="1">
        <name>Filter-width</name>
    </parameter>
    <parameter type="constant" name="av.m" default="1" min="0" max="1" decimals="2">
        <name>Mix</name>
    </parameter>
    <parameter type="bool" name="av.n" default="0">
        <name>Normalize</name>
    </parameter>
    <parameter type="list" name="av.a" default="di" paramlist="di;dii;tdii;latt">
        <paramlistdisplay>di,dii,tdii,latt</paramlistdisplay>
        <name>Transform type</name>
    </parameter>
    <parameter type="list" name="av.r" default="auto" paramlist="auto;s16;s32;f32;f64">
        <paramlistdisplay>Auto,s16,s32,f32,f64</paramlistdisplay>
        <name>Filter precision</name>
    </parameter>
</effect>
