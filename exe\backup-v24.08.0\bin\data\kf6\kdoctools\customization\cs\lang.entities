<!ENTITY language "cs">

<!-- These entities should be translated, should therefore be stored
     separately. -->
<!ENTITY % kde.translated
                            SYSTEM "strings.entities"                >
%kde.translated;

<!-- The following entities should only use &kappname; in their 
     text                                                          -->

<!-- Licence links -->
<!ENTITY underGPL           PUBLIC "-//KDE//DOCUMENT GPL Licence Declaration//CS"
  "entities/underGPL.docbook"                       ><!-- level: para -->
<!ENTITY underFDL           PUBLIC "-//KDE//DOCUMENT FDL Licence Declaration//CS"
  "entities/underFDL.docbook"                       ><!-- level: para -->
<!ENTITY underBSDLicense    PUBLIC "-//KDE//DOCUMENT BSD Licence Declaration//CS"
  "entities/underBSDLicense.docbook"                ><!-- level: para -->
<!ENTITY underArtisticLicense PUBLIC "-//KDE//DOCUMENT Artistic Licence Declaration//CS"
  "entities/underArtisticLicense.docbook"           ><!-- level: para -->
<!ENTITY underX11License    PUBLIC "-//KDE//DOCUMENT X11 Licence Declaration//CS"
  "entities/underX11License.docbook"                ><!-- level: para -->
<!ENTITY underLGPL          PUBLIC "-//KDE//DOCUMENT LGPL Licence Declaration//UK"
 "entities/underLGPL.docbook"                       ><!-- level: para -->
<!ENTITY underCCBYSA4       PUBLIC "-//KDE//DOCUMENT CC BY-SA 4.0 Licence Declaration//CS"
  "../en/entities/underCCBYSA4.docbook"                   ><!-- level: para -->

<!ENTITY reporting.bugs     PUBLIC "-//KDE//DOCUMENT Report Bugs//CS"
  "entities/report-bugs.docbook"                       ><!-- level: ? -->
<!ENTITY updating.documentation PUBLIC "-//KDE//DOCUMENT Updating Documentation//CS"
  "entities/update-doc.docbook"                     ><!-- level: para -->
<!ENTITY help.menu.documentation PUBLIC "-//KDE//DOCUMENT Help Menu Documentation//CS"
  "entities/help-menu.docbook"                      ><!-- level: variablelist -->
<!ENTITY install.intro.documentation PUBLIC "-//KDE//DOCUMENT Installation General Information//CS"
  "entities/install-intro.docbook"                     ><!-- level: para -->
<!ENTITY install.compile.documentation PUBLIC "-//KDE//DOCUMENT Compilation Instructions//CS"
  "entities/install-compile.docbook"                     ><!-- level: para -->


<!-- CC BY-SA 4 notice -->
<!-- In order to translate it, copy it into <lang>/entities/ccbysa4-notice.docbook
and change the reference from English version to the translated document
and remove this comment. -->
<!ENTITY CCBYSA4Notice PUBLIC "-//KDE//DOCUMENT CC BY-SA 4 Documentation Notice//CS"
         "../en/entities/ccbysa4-notice.docbook">
<!-- FDL notice -->
<!ENTITY FDLNotice PUBLIC "-//KDE//DOCUMENT GNU Free Documentation Notice//CS"
         "entities/fdl-notice.docbook">
<!-- meant to be included, so no NDATA or CDATA (why?) -->

<!-- These entities may be extended by the authors and translators.
     They should therefore be stored separately.  Moreover, they MUST
     come last, to avoid overriding problems. -->
<!ENTITY % kde.language.specific
                            SYSTEM "user.entities"                   >
%kde.language.specific;
