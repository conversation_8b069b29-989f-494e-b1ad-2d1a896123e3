<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="dynamictext" id="dynamictext">
    <name>Dynamic Text</name>
    <description>Overlay text with keywords replaced</description>
    <author><PERSON></author>
    <parameter type="animatedrect" name="geometry" default="0 0 100% 100%" fixed="0">
        <name>Geometry</name>
    </parameter>
    <parameter type="fontfamily" name="family" default="DejaVu Sans">
        <name>Font Family</name>
    </parameter>
    <parameter type="constant" name="size" max="200" min="8" default="48">
        <name>Font Size</name>
    </parameter>
    <parameter type="constant" name="weight" max="900" min="100" default="400">
        <name>Font Weight</name>
    </parameter>
    <parameter type="color" name="fgcolour" default="0x000000ff" alpha="1">
        <name>Foreground Color</name>
    </parameter>
    <parameter type="color" name="bgcolour" default="0x00000020" alpha="1">
        <name>Background Color</name>
    </parameter>
    <parameter type="color" name="olcolour" default="0x00000000" alpha="1">
        <name>Outline Color</name>
    </parameter>
    <parameter type="constant" name="outline" max="3" min="0" default="0">
        <name>Outline Width</name>
    </parameter>
    <parameter type="constant" name="pad" max="500" min="0" default="0">
        <name>Padding</name>
    </parameter>
    <parameter type="list" name="halign" default="left" paramlist="left;centre;right">
        <paramlistdisplay>Left,Center,Right</paramlistdisplay>
        <name>Horizontal Alignment</name>
    </parameter>
    <parameter type="list" name="valign" default="top" paramlist="top;middle;bottom">
        <paramlistdisplay>Top,Middle,Bottom</paramlistdisplay>
        <name>Vertical Alignment</name>
    </parameter>
    <parameter type="keywords" name="argument" default="#timecode#" paramlist="#timecode#;#frame#;#createdate#;#filedate#;#localfiledate#;#meta.media.0.stream.frame_rate#;#meta.media.0.codec.name#;#meta.media.0.codec.bit_rate#;#meta.media.width#;#meta.media.height#;#meta.attr.comment.markup#">
        <name>Text</name>
        <paramlistdisplay>timecode,frame,creation date,file date (GMT),file date (local),source frame rate,source codec,source bit rate,source width,source height,source comment</paramlistdisplay>
        <comment>You can also enter strftime parameters to customize the output, like: #createdate %H:%M %D#</comment>
    </parameter>
</effect>
