<!-- Do NOT change this file: it provides important defaults -->
<!-- test -->
<!ENTITY language "en">

<!-- These entities should be translated, should therefore be stored
     separately. -->
<!ENTITY % kde.translated
                            SYSTEM "strings.entities"                >
%kde.translated;

<!-- The following entities should only use &kappname; in their 
     text                                                          -->

<!-- Licence links -->
<!ENTITY underGPL           PUBLIC "-//KDE//DOCUMENT GPL Licence Declaration//EN"
  "entities/underGPL.docbook"                       ><!-- level: para -->
<!ENTITY underLGPL          PUBLIC "-//KDE//DOCUMENT LGPL Licence Declaration//EN"
  "entities/underLGPL.docbook"                       ><!-- level: para -->
<!ENTITY underCCBYSA4       PUBLIC "-//KDE//DOCUMENT CC BY-SA 4.0 Licence Declaration//EN"
  "entities/underCCBYSA4.docbook"                   ><!-- level: para -->
<!ENTITY underFDL           PUBLIC "-//KDE//DOCUMENT FDL Licence Declaration//EN"
  "entities/underFDL.docbook"                       ><!-- level: para -->
<!ENTITY underBSDLicense    PUBLIC "-//KDE//DOCUMENT BSD Licence Declaration//EN"
  "entities/underBSDLicense.docbook"                ><!-- level: para -->
<!ENTITY underArtisticLicense PUBLIC "-//KDE//DOCUMENT Artistic Licence Declaration//EN"
  "entities/underArtisticLicense.docbook"           ><!-- level: para -->
<!ENTITY underX11License    PUBLIC "-//KDE//DOCUMENT X11 Licence Declaration//EN"
  "entities/underX11License.docbook"                ><!-- level: para -->

<!ENTITY reporting.bugs     PUBLIC "-//KDE//DOCUMENT Report Bugs//EN"
  "entities/report-bugs.docbook"                       ><!-- level: ? -->
<!ENTITY updating.documentation PUBLIC "-//KDE//DOCUMENT Updating Documentation//EN"
  "entities/update-doc.docbook"                     ><!-- level: para -->
<!ENTITY help.menu.documentation PUBLIC "-//KDE//DOCUMENT Help Menu Documentation//EN"
  "entities/help-menu.docbook"                      ><!-- level: variablelist -->

<!-- Entities install.intro.documentation + install.compile.documentation
     are deprecated and should be removed in Frameworks 6 -->

<!ENTITY install.intro.documentation PUBLIC "-//KDE//DOCUMENT Installation General Information//EN"
  "entities/install-intro.docbook"                     ><!-- level: para -->
<!ENTITY install.compile.documentation PUBLIC "-//KDE//DOCUMENT Compilation Instructions//EN"
  "entities/install-compile.docbook"                     ><!-- level: para -->


<!-- CC BY-SA 4 notice -->
<!ENTITY CCBYSA4Notice PUBLIC "-//KDE//DOCUMENT CC BY-SA 4 Documentation Notice//EN"
         "entities/ccbysa4-notice.docbook">
<!-- FDL notice -->
<!ENTITY FDLNotice PUBLIC "-//KDE//DOCUMENT GNU Free Documentation Notice//EN"
         "entities/fdl-notice.docbook">
<!-- meant to be included, so no NDATA or CDATA (why?) -->

<!-- These entities may be extended by the authors and translators.
     They should therefore be stored separately.  Moreover, they MUST
     come last, to avoid overriding problems. -->
<!ENTITY % kde.language.specific
                            SYSTEM "user.entities"                   >
%kde.language.specific;
