<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.acrusher" id="avfilter.acrusher" type="audio">
    <name>Crusher</name>
    <description>Reduce audio bit resolution.</description>
    <author>libavfilter</author>
    <parameter type="constant" name="av.level_in" default="1" min="0.015" max="64" decimals="3">
        <name>Input gain</name>
    </parameter>
    <parameter type="constant" name="av.level_out" default="1" min="0.015" max="64" decimals="3">
        <name>Output gain</name>
    </parameter>
    <parameter type="constant" name="av.limit" default="1" min="0.06" max="1" decimals="2">
        <name>Limit</name>
    </parameter>
    <parameter type="constant" name="av.bits" default="8" min="1" max="64" suffix="bits">
        <name>Bit reduction</name>
    </parameter>
    <parameter type="constant" name="av.mix" default="0.5" min="0" max="1" decimals="2">
        <name>Mix</name>
    </parameter>
    <parameter type="list" name="av.mode" default="lin" paramlist="lin;log">
        <paramlistdisplay>Linear,Logarithmic</paramlistdisplay>
        <name>Mode</name>
    </parameter>
    <parameter type="constant" name="av.dc" default="1" min="0.25" max="4" decimals="2">
        <name>DC</name>
    </parameter>
    <parameter type="constant" name="av.aa" default="0.5" min="0" max="1" decimals="2">
        <name>Anti-aliasing</name>
    </parameter>
    <parameter type="constant" name="av.samples" default="1" min="1" max="250" suffix="samples">
        <name>Sample reduction</name>
    </parameter>
    <parameter type="bool" name="av.lfo" default="0">
        <name>Enable LFO</name>
    </parameter>
    <parameter type="constant" name="av.lforange" default="20" min="1" max="250">
        <name>LFO depth</name>
    </parameter>
    <parameter type="constant" name="av.lforate" default="0.3" min="0.01" max="200" decimals="2">
        <name>LFO rate</name>
    </parameter>
</effect>
