<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.waveform" id="avfilter.waveform">
    <name>Video waveform monitor</name>
    <description>The waveform monitor plots color component intensity. By default luminance only. Each column of the waveform corresponds to a column of pixels in the source video. </description>
    <author>libavfilter</author>
    <parameter type="list" name="av.m" default="column" paramlist="row;column">
        <paramlistdisplay>Row,Column</paramlistdisplay>
        <name>Mode</name>
    </parameter>
    <parameter type="constant" name="av.i" default="0.04" min="0" max="1" decimals="2">
        <name>Intenstiy</name>
    </parameter>
    <parameter type="bool" name="av.r" default="0                ">
        <name>Mirror</name>
    </parameter>
    <parameter type="list" name="av.d" default="stack" paramlist="overlay;stack;parade">
        <paramlistdisplay>Overlay,Stack,Parade</paramlistdisplay>
        <name>Display</name>
    </parameter>
    <parameter type="constant" name="av.c" default="1" min="1" max="7" factor="1">
        <name>Components</name>
    </parameter>
    <parameter type="list" name="av.e" default="none" paramlist="none;instant;peak;peak+instant">
        <paramlistdisplay>None,Instant,Peak,Peak+Instant</paramlistdisplay>
        <name>Envelope</name>
    </parameter>
    <parameter type="list" name="av.f" default="lowpass" paramlist="lowpass;flat;aflat;xflat;yflat;chroma;color;acolor">
        <paramlistdisplay>lowpass,flat,aflat,xflat,yflat,chroma,color,acolor</paramlistdisplay>
        <name>Filter</name>
    </parameter>
    <parameter type="list" name="av.g" default="green" paramlist="none;green;orange;invert">
        <paramlistdisplay>none,green,orange,invert</paramlistdisplay>
        <name>Graticule</name>
    </parameter>
    <parameter type="constant" name="av.o" default="0.75" min="0" max="1" decimals="2">
        <name>Graticule Opacity</name>
    </parameter>
    <parameter type="list" name="av.fl" default="numbers" paramlist="numbers;dots">
        <paramlistdisplay>numbers,dots</paramlistdisplay>
        <name>Flags</name>
    </parameter>
    <parameter type="list" name="av.s" default="digital" paramlist="digital;millivolts;ire">
        <paramlistdisplay>digital,millivolts,ire</paramlistdisplay>
        <name>Scale</name>
    </parameter>
    <parameter type="constant" name="av.b" default="0.75" min="0" max="1" decimals="2">
        <name>Background Opacity</name>
    </parameter>
</effect>
