<!-- -*- dtd -*-
    Modified hierarchy elements for DocBook as used in the KDE documentation
    (instantiates %rdbhier2;).  The actual changes are done in
    modifications.elements.
    
    SPDX-FileCopyrightText: 2002 <PERSON><PERSON><PERSON>

    SPDX-License-Identifier: GPL-2.0-or-later
    
    Send suggestions, comments, etc. to the KDE docbook list 
    <<EMAIL>>.

    USAGE

    Refer to this DTD as

      "-//KDE//ELEMENTS DocBook Hierarchy Redeclarations 2 V1.0//EN"

    For instance

      <!ENTITY % rdbhier2 PUBLIC
       "-//KDE//ELEMENTS DocBook Hierarchy Redeclarations 2 V1.0//EN">

    Set to IGNORE:                      to revert from KDE customisation for:
    kde.remove.unused.elements          some elements unlikely to be used
    kde.book.meta.info                  extra requirements for bookinfo element
-->


<!-- Elements which are unlikely to be useful in the KDE documentation,
     because their purpose is either to markup already existing documents
     or because there are automatic means of obtaining the same effect 

     %kde.remove.unused.elements; is set in kde-rdbpool.elements.
 -->
<![ %kde.remove.unused.elements; [
<!ENTITY % toc.content.module "IGNORE">
<!ENTITY % lot.content.module "IGNORE">
<!-- end of kde.remove.unused.elements -->]]>


<!-- For translation and revision bookkeeping mechanisms, some elements 
     are required to be present in bookinfo.
 -->
<!ENTITY % kde.book.meta.info "INCLUDE">
<![ %kde.book.meta.info; [
<!ENTITY % book.element "IGNORE">
<!ENTITY % bookinfo.element "IGNORE">
<!-- end of kde.book.meta.info -->]]>
