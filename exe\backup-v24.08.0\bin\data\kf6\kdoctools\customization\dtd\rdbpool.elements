<!-- -*- dtd -*-
    Modified element pool for DocBook as used in the KDE documentation
    (instantiates %rdbpool;)
    
    SPDX-FileCopyrightText: 2001, 2002 <PERSON><PERSON><PERSON>

    SPDX-License-Identifier: GPL-2.0-or-later
    
    Send suggestions, comments, etc. to the KDE docbook list 
    <<EMAIL>>.

    USAGE

    Refer to this DTD as

      "-//KDE//ELEMENTS DocBook XML Pool Redeclarations V1.1//EN"

    For instance

      <!ENTITY % rdbpool PUBLIC
       "-//KDE//ELEMENTS DocBook XML Pool Redeclarations V1.1//EN">

    Set to IGNORE:                      to revert from KDE customisation for:
    kde.remove.unused.elements          some elements unlikely to be used
-->

<!-- Elements which are unlikely to be useful in the KDE documentation,
     because their purpose is either to markup already existing documents
     or because there are automatic means of obtaining the same effect 
 -->
<!ENTITY % kde.remove.unused.elements "INCLUDE">
<![ %kde.remove.unused.elements; [
<!ENTITY % beginpage.module "IGNORE">
<!ENTITY % bridgehead.module "IGNORE">
<!ENTITY % confgroup.content.module "IGNORE">
<!ENTITY % graphic.module "IGNORE">
<!ENTITY % graphicco.module "IGNORE">
<!ENTITY % inlinegraphic.module "IGNORE">
<!ENTITY % revhistory.content.module "IGNORE">
<!-- end of kde.remove.unused.elements -->]]>

<!-- KDE Languages
     To keep the language tags conform to RFC 3066 (successor to 1766)
     (http://www.rfc-editor.org/rfc/rfc3066.txt)
     Syntax (RE): [a-zA-Z]{1,8}(-[a-zA-Z]{1,8})*

     The compulsory part is either a language tag from ISO 639 (ISO
     639-1 (2 character tag) if there is a tag, ISO 639-2/T (three
     character tag) otherwise, "i" (for IANA) or "x" (for private use,
     e.g. languages that are not in ISO 639).
     The optional part may consist of any value.  However, if the
     first repetition consists of two letters, it must be an ISO
     3166a2 country tag (so Nynorsk cannot be indicated by no-NY or
     no-ny).
     Official site for ISO 639-2: http://lcweb.loc.gov/standards/iso639-2/
     For ISO 639-1, only the latest version (currently Feb 2000) counts!

     Countries are currently only needed when the language is different.
 -->
<!ENTITY % lang.attrib
        "lang           (af|ar|az|be|bg|bn|bo|br|bs|ca|ca_valencia|cs|cy|da|de
                        |el|en|en-GB|en-US|eo|es|et|eu|fa|fi|fo|fr|fy|ga|gl|gu
                        |he|hi|hr|hu|id|is|it|ja|kn|km|ko|ku|lo|lt|lv|mi|mk|mn
                        |mr|ms|mt|nb|nds|nl|nn|no|nso|oc|pl|pt|pt-BR|pt-PT|ro
                        |ru|se|si|sk|sl|sq|sr|sr_latin|sr_ijekavian
                        |sr_ijekavianlatin|ss|st|sv|ta|te|tg|th|tr|uk|uz|ven|vi
                        |wa|xh|zh|zh-CN|zh-TW|zu)
                        #IMPLIED">
<!-- List of languages
     (Languages that only have a three-character code from 639-2, must use 
     that one)
af      Afrikaans
ar	Arabic
az	Azerbaijani
be	Belarusian
bg      Bulgarian
bn	Bengali
bo      Tibetan
br      Breton
bs	Bosnian
ca      Catalan
cs      Czech
cy      Welsh
da      Danish
de      German
el      Greek, Modern (1453-)
en      English
eo      Esperanto
es      Spanish
et      Estonian
eu      Basque
fa      Persian (Farsi)
fi      Finnish
fo      Faroese
fr      French
fy      Frisian
ga      Irish
gl	Galician
gu	Gujarati
he      Hebrew
hi      Hindi
hr      Croatian
hu      Hungarian
id      Indonesian
is      Icelandic
it      Italian
ja      Japanese
km	Khmer
kn      Kannada
ko      Korean
ku	Kurdish
lo      Lao
lt      Lituanian
lv	Latvian
mi      Maori
mk      Macedonian
mn      Mongolian
mr	Marathi
ms      Malay
mt	Maltese
nb      Norwegian Bokmal
nds     Low Saxon, Low German
nl      Dutch
nn      Norwegian Nynorsk
no      Norwegian (in KDE = nb)
nso	Northern Sotho
oc	Occitan (post 1500); Provençal
pl      Polish
pt      Portuguese
ro      Romanian
ru      Russian
se      Northern Sami
si      Sinhalese
sk      Slovak
sl      Slovenian
sq	Albanian
sr      Serbian
ss      Swati
st	Sesotho (souther Sotho)
sv      Swedish
ta      Tamil
te      Telugu
tg	Tajik
th      Thai
tr      Turkish
uk      Ukrainian
uz      Uzbek
ven	Venda
vi	Vietnamese
wa	Walloon
xh	Xhosa
zh      Chinese
zu	Zulu
-->
<!-- end of KDE languages -->
<!-- Languages could be made compulsory for books -->

<!ENTITY % remap.attrib "">

<!-- Not used -->
<!ENTITY % revisionflag.attrib "">

<!ENTITY % othercredit.role.attrib
        "role   (translator|developer|reviewer|graphist|musician|tester)
                #REQUIRED">

<!ENTITY % kde.compulsory.datereleaseinfo "INCLUDE">
<![ %kde.compulsory.datereleaseinfo; [
<!-- Unchanged -->
<!ENTITY % local.person.ident.mix "">
<!ENTITY % person.ident.mix
                "honorific|firstname|surname|lineage|othername|affiliation
                |authorblurb|contrib %local.person.ident.mix;">

<!ENTITY % local.bibliocomponent.mix "">
<!-- Copy of bibliocomponent.mix without ReleaseInfo, Date or Abstract -->
<!ENTITY % kde.bibliocomponent.mix
		"abbrev|address|artpagenums|author
		|authorgroup|authorinitials|bibliomisc|biblioset
		|collab|confgroup|contractnum|contractsponsor
		|copyright|corpauthor|corpname|edition
		|editor|invpartnumber|isbn|issn|issuenum|orgname
		|biblioid|citebiblioid|bibliosource|bibliorelation|bibliocoverage
		|othercredit|pagenums|printhistory|productname
		|productnumber|pubdate|publisher|publishername
		|pubsnumber|revhistory|seriesvolnums
		|subtitle|title|titleabbrev|volumenum|citetitle
		|personname|%person.ident.mix;
		|%ndxterm.class;
		%local.bibliocomponent.mix;">
<!-- Unchanged -->
<!ENTITY % local.info.class "">
<!-- Copy of info.class with %kde.bibliocomponent.mix; -->
<!ENTITY % kde.info.class
		"graphic | mediaobject | legalnotice | modespec
		 | subjectset | keywordset | itermset | %kde.bibliocomponent.mix;
                 %local.info.class;">
<!-- End of compulsory ReleaseInfo -->]]>

<!-- KDE uses a more specific list of attributes -->
<!ENTITY % olink.attlist "IGNORE">
