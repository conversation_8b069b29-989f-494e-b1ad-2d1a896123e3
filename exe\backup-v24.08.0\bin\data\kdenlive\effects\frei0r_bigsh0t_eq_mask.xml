<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="frei0r.bigsh0t_eq_mask" id="frei0r.bigsh0t_eq_mask">
    <name>VR360 Equirectangular Mask</name>
    <description>Adds a black matte to the frame. Use this if you filmed using a 360 camera but only want to use part of the 360 image - for example if you and the film crew occupy the 90 degrees behind the camera.</description>
    <author><PERSON></author>
    <parameter type="animated" name="hfov0" default="180" min="0" max="360" factor="1" suffix="°">
        <name>Vertical Start</name>
    </parameter>
    <parameter type="animated" name="hfov1" default="200" min="0" max="360" factor="1" suffix="°">
        <name>Vertical End</name>
    </parameter>
    <parameter type="animated" name="vfov0" default="140" min="0" max="360" factor="1" suffix="°">
        <name>Horizontal Start</name>
    </parameter>
    <parameter type="animated" name="vfov1" default="160" min="0" max="360" factor="1" suffix="°">
        <name>Horizontal End</name>
    </parameter>
    <parameter type="list" name="interpolation" default="0" paramlist="0;1">
        <paramlistdisplay>Nearest-Neighbor,Bilinear</paramlistdisplay>
        <name>Interpolation</name>
    </parameter>
</effect>
