<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="affine" id="affine">
    <name>Rotate and <PERSON>r</name>
    <description>Rotate clip in any 3 directions</description>
    <author><PERSON></author>
    <parameter type="constant" name="transition.fix_rotate_x" max="1800" min="-1800" default="0" factor="10">
        <name>Rotate X</name>
    </parameter>
    <parameter type="constant" name="transition.fix_rotate_y" max="1800" min="-1800" default="0" factor="10">
        <name>Rotate Y</name>
    </parameter>
    <parameter type="constant" name="transition.fix_rotate_z" max="1800" min="-1800" default="0" factor="10">
        <name>Rotate Z</name>
    </parameter>
    <parameter type="constant" name="transition.rotate_x" max="200" min="-200" default="0" factor="10">
        <name>Animate Rotate X</name>
    </parameter>
    <parameter type="constant" name="transition.rotate_y" max="200" min="-200" default="0" factor="10">
        <name>Animate Rotate Y</name>
    </parameter>
    <parameter type="constant" name="transition.rotate_z" max="200" min="-200" default="0" factor="10">
        <name>Animate Rotate Z</name>
    </parameter>
    <parameter type="constant" name="transition.fix_shear_x" max="1800" min="-1800" default="0" factor="10">
        <name>Shear X</name>
    </parameter>
    <parameter type="constant" name="transition.fix_shear_y" max="1800" min="-1800" default="0" factor="10">
        <name>Shear Y</name>
    </parameter>
    <parameter type="constant" name="transition.shear_x" max="200" min="-200" default="0" factor="10">
        <name>Animate Shear X</name>
    </parameter>
    <parameter type="constant" name="transition.shear_y" max="200" min="-200" default="0" factor="10">
        <name>Animate Shear Y</name>
    </parameter>
    <parameter type="animatedrect" name="transition.rect" default="0 0 100% 100%" fixed="1" opacity="false">
        <name>Pan and Zoom</name>
    </parameter>
    <parameter type="fixedcolor" name="producer.resource" default="0x00000000" alpha="1">
        <name>Background Color</name>
    </parameter>
    <parameter type="bool" name="transition.repeat_off" default="1">
        <name>Disable repeat</name>
    </parameter>
    <parameter type="bool" name="transition.mirror_off" default="1">
        <name>Disable mirror</name>
    </parameter>
</effect>
