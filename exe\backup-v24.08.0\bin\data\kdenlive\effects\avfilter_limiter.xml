<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.limiter" id="avfilter.limiter">
    <name>Limiter</name>
    <description>Limits the pixel components values to the specified range [min, max]</description>
    <author>libavfilter</author>
    <parameter type="constant" name="av.min" default="0" min="0" max="255" factor="1">
        <name>Min</name>
        <comment>The Min value has not to be higher than the Max value</comment>
    </parameter>
    <parameter type="constant" name="av.max" default="65535" min="0" max="255" factor="1">
        <name>Max</name>
        <comment>The Max value has not to be lower than the Min value</comment>
    </parameter>
    <parameter type="list" name="av.planes" default="7" paramlist="0;1;2;3;4;5;6;7;8">
        <paramlistdisplay>None,Y,U,YU,V,YV,UV,YUV,Alpha</paramlistdisplay>
        <name>Planes</name>
    </parameter>
</effect>
