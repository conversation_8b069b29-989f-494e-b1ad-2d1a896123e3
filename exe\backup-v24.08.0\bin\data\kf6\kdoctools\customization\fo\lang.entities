<!ENTITY language "fo">

<!-- These entities should be translated, should therefore be stored
     separately. -->
<!ENTITY % kde.translated
                            SYSTEM "strings.entities"                >
%kde.translated;

<!-- The following entities should only use &kappname; in their 
     text                                                          -->

<!-- Licence links -->
<!ENTITY underGPL           PUBLIC "-//KDE//DOCUMENT GPL Licence Declaration//FO"
  "entities/underGPL.docbook"                       ><!-- level: para -->
<!ENTITY underCCBYSA4       PUBLIC "-//KDE//DOCUMENT CC BY-SA 4.0 Licence Declaration//FO"
  "../en/entities/underCCBYSA4.docbook"                   ><!-- level: para -->
<!ENTITY underFDL           PUBLIC "-//KDE//DOCUMENT FDL Licence Declaration//FO"
  "entities/underFDL.docbook"                       ><!-- level: para -->
<!ENTITY underBSDLicense    PUBLIC "-//KDE//DOCUMENT BSD Licence Declaration//FO"
  "entities/underBSDLicense.docbook"                ><!-- level: para -->
<!ENTITY underArtisticLicense PUBLIC "-//KDE//DOCUMENT Artistic Licence Declaration//FO"
  "entities/underArtisticLicense.docbook"           ><!-- level: para -->
<!ENTITY underX11License    PUBLIC "-//KDE//DOCUMENT X11 Licence Declaration//FO"
  "entities/underX11License.docbook"                ><!-- level: para -->

<!ENTITY reporting.bugs     PUBLIC "-//KDE//DOCUMENT Report Bugs//FO"
  "entities/report-bugs.docbook"                       ><!-- level: ? -->
<!ENTITY updating.documentation PUBLIC "-//KDE//DOCUMENT Updating Documentation//FO"
  "entities/update-doc.docbook"                     ><!-- level: para -->
<!ENTITY help.menu.documentation PUBLIC "-//KDE//DOCUMENT Help Menu Documentation//FO"
  "entities/help-menu.docbook"                      ><!-- level: variablelist -->
<!ENTITY install.intro.documentation PUBLIC "-//KDE//DOCUMENT Installation General Information//FO"
  "entities/install-intro.docbook"                     ><!-- level: para -->
<!ENTITY install.compile.documentation PUBLIC "-//KDE//DOCUMENT Compilation Instructions//FO"
  "entities/install-compile.docbook"                     ><!-- level: para -->


<!-- CC BY-SA 4 notice -->
<!-- In order to translate it, copy it into <lang>/entities/ccbysa4-notice.docbook
and change the reference from English version to the translated document
and remove this comment. -->
<!ENTITY CCBYSA4Notice PUBLIC "-//KDE//DOCUMENT CC BY-SA 4 Documentation Notice//FO"
         "../en/entities/ccbysa4-notice.docbook">
<!-- FDL notice -->
<!ENTITY FDLNotice PUBLIC "-//KDE//DOCUMENT GNU Free Documentation Notice//FO"
         "entities/fdl-notice.docbook">
<!-- meant to be included, so no NDATA or CDATA (why?) -->

<!-- These entities may be extended by the authors and translators.
     They should therefore be stored separately.  Moreover, they MUST
     come last, to avoid overriding problems. -->
<!ENTITY % kde.language.specific
                            SYSTEM "user.entities"                   >
%kde.language.specific;
