<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.colortemperature" id="avfilter.colortemperature">
    <name>Color Temperature</name>
    <description>Adjust color temperature in video to simulate variations in ambient color temperature. </description>
    <author>libavfilter</author>
    <parameter type="animated" name="av.temperature" default="6500" min="1000" max="40000" decimals="1" suffix="°K">
        <name>Temperature</name>
    </parameter>
    <parameter type="animated" name="av.mix" default="1" min="0" max="1" decimals="3">
        <name>Mix</name>
    </parameter>
    <parameter type="animated" name="av.saturation" default="0.5" min="0" max="1" decimals="3">
        <name>Saturation</name>
    </parameter>
    <parameter type="animated" name="av.pl" default="0" min="0" max="1" decimals="3">
        <name>Preserve lightness</name>
    </parameter>
</effect>
