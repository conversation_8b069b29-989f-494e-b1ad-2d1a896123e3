<!-- These entities should be translated, but NOT CHANGED, NOR EXTENDED.
     For language-specific extensions, use user.entities.
     Translate everything between quotes, except names of general 
     entities (&...;). -->

<!ENTITY kappname "esta aplicación">
<!-- Entities to fill in slots in docbook version of FDL notice.
     The default values of the parameter entities is IGNORE. -->
<![%FDLIS;[
<!ENTITY FDLISTitles "LISTADOS SUS TITULOS"><!-- keep capitals -->
<!ENTITY FDLInvariantSections "las secciones invariantes estando &FDLISTitles;">
 ]]>
<!ENTITY FDLInvariantSections "secciones no invariantes">
<![%FDLFCT;[
<!ENTITY FDLFCTTitles "LISTADOS"><!-- keep capitals -->
<!ENTITY FDLFrontCoverText "los textos de la portada estando &FDLFCTTitles;">
 ]]>
<!ENTITY FDLFrontCoverText "textos que no estén en la portada">
<![%FDLBCT;[
<!ENTITY FDLBCTTitles "LISTADOS SUS TITULOS"><!-- keep capitals -->
<!ENTITY FDLBackCoverText "los textos en la contraportada estando &FDLBCTTitles;">
 ]]>
<!ENTITY FDLBackCoverText "textos que no estén en la contraportada">

<!-- modespec entity: must be adapted in accordance with the normal usage
     for documents in a language; the most likely candidates are the value
     of xreflabel (now %t for title of section referred to) and the content
     (now empty).  If more than one format is needed, contact <EMAIL>.
     ** In general, this setup will not work with more than one language in 
        a document **
     Usage: in <bookinfo>
     Only strictly needed when olinks are used
 -->
<!--ENTITY kde-modespec '<modespec id="kdems-default" xreflabel="&percnt;t"></modespec>'-->
<!ENTITY kde.modespec '
 <modespec id="kdems-help">help:</modespec>
 <modespec id="kdems-man">man:</modespec>'>

<!ENTITY olinktype "kde-installation">
