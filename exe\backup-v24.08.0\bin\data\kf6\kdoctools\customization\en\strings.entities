<!-- These entities should be translated, but NOT CHANGED, NOR EXTENDED.
     For language-specific extensions, use user.entities.
     Translate everything between quotes, except names of general 
     entities (&...;). -->

<!ENTITY kappname "this application">
<!-- Entities to fill in slots in docbook version of FDL notice.
     The default values of the parameter entities is IGNORE. -->
<![%FDLIS;[
<!ENTITY FDLISTitles "LIST THEIR TITLES"><!-- keep capitals -->
<!ENTITY FDLInvariantSections "the Invariant Sections being &FDLISTitles;">
 ]]>
<!ENTITY FDLInvariantSections "no Invariant Sections">
<![%FDLFCT;[
<!ENTITY FDLFCTTitles "LIST"><!-- keep capitals -->
<!ENTITY FDLFrontCoverText "the Front-Cover Texts being &FDLFCTTitles;">
 ]]>
<!ENTITY FDLFrontCoverText "no Front-Cover Texts">
<![%FDLBCT;[
<!ENTITY FDLBCTTitles "LIST THEIR TITLES"><!-- keep capitals -->
<!ENTITY FDLBackCoverText "the Back-Cover Texts being &FDLBCTTitles;">
 ]]>
<!ENTITY FDLBackCoverText "no Back-Cover Texts">

<!-- modespec entity: must be adapted in accordance with the normal usage
     for documents in a language; the most likely candidates are the value
     of xreflabel (now %t for title of section referred to) and the content
     (now empty).  If more than one format is needed, contact <EMAIL>.
     ** In general, this setup will not work with more than one language in 
        a document **
     Usage: in <bookinfo>
     Only strictly needed when olinks are used
 -->
<!--ENTITY kde-modespec '<modespec id="kdems-default" xreflabel="&percnt;t"></modespec>'-->
<!ENTITY kde.modespec '
 <modespec id="kdems-help">help:</modespec>
 <modespec id="kdems-man">man:</modespec>'>

<!ENTITY olinktype "kde-installation">
