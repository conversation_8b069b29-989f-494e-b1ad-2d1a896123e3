<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.colorcorrect" id="avfilter.colorcorrect">
    <name>Color Correct</name>
    <description>Adjust color white balance selectively for blacks and whites.This filter operates in YUV colorspace.</description>
    <author>libavfilter</author>
    <parameter type="animated" name="av.rl" default="0" min="-1" max="1" decimals="3">
        <name>Red shadow spot</name>
    </parameter>
    <parameter type="animated" name="av.bl" default="0" min="-1" max="1" decimals="3">
        <name>Blue shadow spot</name>
    </parameter>
    <parameter type="animated" name="av.rh" default="0" min="-1" max="1" decimals="3">
        <name>Red highlight spot</name>
    </parameter>
    <parameter type="animated" name="av.bh" default="0" min="-1" max="1" decimals="3">
        <name>Blue highlight spot</name>
    </parameter>
    <parameter type="animated" name="av.saturation" default="1" min="-3" max="3" decimals="3">
        <name>Saturation</name>
    </parameter>
    <parameter type="list" name="av.analyze" default="manual" paramlist="manual;average;minimax;median">
        <paramlistdisplay>Manual,Average,Minimax,Median</paramlistdisplay>
        <name>Analyze mode</name>
    </parameter>
</effect>
