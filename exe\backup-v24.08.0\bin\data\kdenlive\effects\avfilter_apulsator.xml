<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.apulsator" id="avfilter.apulsator" type="audio">
    <name>Pulsator</name>
    <description> Audio pulsator.</description>
    <author>libavfilter</author>
    <parameter type="constant" name="av.level_in" default="1" min="0.015" max="64" decimals="3">
        <name>Input gain</name>
    </parameter>
    <parameter type="constant" name="av.level_out" default="1" min="0.015" max="64" decimals="3">
        <name>Output gain</name>
    </parameter>
    <parameter type="list" name="av.mode" default="sine" paramlist="sine;triangle;square;sawup;sawdown">
        <paramlistdisplay>sine,triangle,square,sawup,sawdown</paramlistdisplay>
        <name>Mode</name>
    </parameter>
    <parameter type="constant" name="av.amount" default="1" min="0.00" max="1" decimals="2">
        <name>Modulation</name>
    </parameter>
    <parameter type="constant" name="av.offset_l" default="0" min="0" max="1" decimals="2">
        <name>Offset L</name>
    </parameter>
    <parameter type="constant" name="av.offset_r" default="0.5" min="0" max="1" decimals="2">
        <name>Offset R</name>
    </parameter>
    <parameter type="constant" name="av.width" default="1" min="0" max="2" decimals="2">
        <name>Pulse width</name>
    </parameter>
    <parameter type="list" name="av.timing" default="bpm" paramlist="bpm;ms;hz">
        <paramlistdisplay>bpm,ms,Hz</paramlistdisplay>
        <name>Timing</name>
    </parameter>
    <parameter type="constant" name="av.bpm" default="120" min="30" max="300" suffix="bpm">
        <name>BPM</name>
    </parameter>
    <parameter type="constant" name="av.ms" default="500" min="10" max="2000" suffix="ms">
        <name>Milliseconds</name>
    </parameter>
    <parameter type="constant" name="av.hz" default="2" min="0.01" max="100" decimals="2">
        <name>Frequency in Hz</name>
        <comment>Only used if timing is set to hz.</comment>
    </parameter>
</effect>
