<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="loudness" id="loudness" type="audio" condition="results">
    <name>Normalize (2 pass)</name>
    <description>Correct audio loudness as recommended by EBU R128</description>
    <author><PERSON></author>
    <parameter type="double" name="program" max="-10" min="-50" default="-23.00" decimals="2" suffix="LUFS">
        <name>Target Program Loudness</name>
    </parameter>
    <parameter type="hidden" name="results" default="">
    </parameter>
    <parameter type="filterjob" filtertag="loudness" filterparams="%params" consumer="null" consumerparams="video_off=1 no_meta=1 all=1 terminate_on_pause=1">
        <name conditional="Reset">Analyse to Apply Effect</name>
        <jobparam name="conditionalinfo">Click Analyse to Apply Effect</jobparam>
        <jobparam name="key">results</jobparam>
        <jobparam name="keydefault"></jobparam>
        <jobparam name="finalfilter">loudness</jobparam>
        <jobparam name="relativeInOut">1</jobparam>
    </parameter>
</effect>
