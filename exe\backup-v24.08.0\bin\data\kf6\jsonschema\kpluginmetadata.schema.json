{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "bla": "", "properties": {"KPlugin": {"properties": {"Name": {"type": "string"}, "Description": {"type": "string"}, "Icon": {"type": "string"}, "Authors": {"type": "array", "items": {"$ref": "#/definitions/KAboutPerson"}}, "Category": {"type": "string"}, "License": {"type": "string"}, "Copyright": {"type": "string"}, "Id": {"type": "string"}, "Version": {"type": "string"}, "Website": {"type": "string"}, "BugReportUrl": {"type": "string"}, "EnabledByDefault": {"type": "boolean"}, "MimeTypes": {"type": "array", "items": {"type": "string"}}, "FormFactors": {"type": "array", "items": {"type": "string"}}, "Translators": {"type": "array", "items": {"$ref": "#/definitions/KAboutPerson"}}, "OtherContributors": {"type": "array", "items": {"$ref": "#/definitions/KAboutPerson"}}}}}, "definitions": {"KAboutPerson": {"type": "object", "properties": {"Name": {"type": "string"}, "Email": {"type": "string"}, "Task": {"type": "string"}, "Website": {"type": "string"}, "AvatarUrl": {"type": "string"}}, "required": ["Name"]}}}