<!--
    KDE general entities for DocBook as used in the KDE documentation
    
    SPDX-FileCopyrightText: 2002 <PERSON><PERSON><PERSON>

    SPDX-License-Identifier: GPL-2.0-or-later
    
    Send suggestions, comments, etc. to the KDE docbook list 
    <<EMAIL>>.


    USAGE

    Refer to this file as

      "-//KDE//ENTITIES DocBook XML General Entity Declarations V1.2//EN"

    This file contains what dbgenent.mod should contain and is read
    after all other files just like dbgenent.mod should be.

-->

<!-- ============================================================= -->
<!--		    Extensions to the DocBook DTD		   -->
<!-- ============================================================= -->
<!-- E.g. KDE specific entities (see also dbgenent.mod for this)   -->

<!-- Legal texts we put in a specific place
     so that we don't need to cut'n'paste them to every doc
 -->
<!ENTITY GPL     	    PUBLIC
  "-//GNU//DOCUMENT GNU General Public License V2//EN"
  "http://www.gnu.org/copyleft/gpl.html"
  NDATA linespecific	                                             >
<!ENTITY GPLNotice	    PUBLIC
  "-//KDE//DOCUMENT GNU General Public License Notice//EN"
  "../en/entities/gpl-notice.docbook"><!-- meant for inclusion, so no [CN]DATA SGML (why?) -->
<!ENTITY LGPL		    PUBLIC
  "-//GNU//DOCUMENT GNU Lesser General Public License V2.1//EN"
  "http://www.gnu.org/copyleft/lesser.html"
  NDATA linespecific	                                             >
<!ENTITY LGPLNotice	    PUBLIC
  "-//KDE//DOCUMENT GNU Lesser General Public License Notice//EN"
  "../en/entities/lgpl-notice.docbook"><!-- meant for inclusion, so no [CN]DATA SGML -->
<!ENTITY FDL                PUBLIC
  "-//GNU//DOCUMENT GNU Free Documentation License V1.1//EN"
  "http://www.gnu.org/copyleft/fdl.html"
  NDATA linespecific                                                 >
<!-- FDLNotice is defined in lang.entities (why?) -->
<!-- CCBYSA4Notice is defined in lang.entities (translatable) -->

<!-- add docbook versions of licenses -->

<!ENTITY % kde.writer.entities	PUBLIC
  "-//KDE//ENTITIES DocBook XML General Entity Declarations: Contributors V1.0//EN"    
  "../entities/contributor.entities"                                 >
%kde.writer.entities;

<!-- KDE index generation
     Set addindex to IGNORE to stop index generation
     Indices are generated on the fly, so no intermediate file is needed
 -->
<!ENTITY % addindex "INCLUDE">
<![%addindex;[
<!ENTITY documentation.index "<index id='doc-index'></index>">
]]>
<!ENTITY documentation.index "">
<!--ENTITY kapp "(Oops - someone forgot to fill in the application name here)" -->

<!-- Entities to fill in slots in docbook version of FDL notice -->
<!ENTITY % FDLIS "IGNORE">
<!ENTITY % FDLFCT "IGNORE">
<!ENTITY % FDLBCT "IGNORE">

<!-- These are the language-independent entities.  They can be (more
     or less) freely extended.  Keep this list sorted and sensible.  
     Make sure you always add full markup to the text.

     Naming policy: name the entity like you would see the text
     (inclusive case).  KDE entities are always lowercased.  Spaces
     are omitted.  Examples: Qt (for "Qt"), RedHat (for "Red Hat"),
     kfloppy (for "KFloppy") .  (Entity names cannot start with digits,
     in that case, use the written form for the first digit.)

     DO NOT JUST RENAME ENTITIES!  That implies removal of an existing
     entity and that may change the validity of documents, which must
     not happen.  Place the name you wish to remove in the list of
     obsolete entities (depending on the case, the expansion is
     redefined or kept as it was); the new name goes in the official list.
     A later version of the DTD (with another version number!) will 
     effectively remove these entities.  In that way, old documents 
     remain valid, while new ones can only use the new declarations.
 -->

<!ENTITY acl     "<acronym>ACL</acronym>">
<!ENTITY AIX	"<trademark class='registered'>AIX</trademark>">
<!ENTITY akonadi	"<acronym>Akonadi</acronym>">
<!ENTITY akregator        "<application>Akregator</application>">
<!ENTITY amarok	"<application>Amarok</application>">
<!ENTITY amor	"<application>AMOR</application>">
<!ENTITY ark	"<application>Ark</application>">
<!ENTITY artikulate	"<application>Artikulate</application>">
<!ENTITY ASCII	"<acronym>ASCII</acronym>">
<!ENTITY ATAPI	"<acronym>ATAPI</acronym>">
<!ENTITY blinken	"<application>Blinken</application>">
<!ENTITY blogilo "<application>Blogilo</application>">
<!ENTITY bomber "<application>Bomber</application>">
<!ENTITY bovo	"<application>Bovo</application>">
<!ENTITY BSD "<trademark>BSD</trademark>">
<!ENTITY calligra "Calligra">
<!ENTITY calligraflow "<application>Calligra Flow</application>">
<!ENTITY calligraplan "<application>Calligra Plan</application>">
<!ENTITY calligrasheets "<application>Calligra Sheets</application>">
<!ENTITY calligrastage "<application>Calligra Stage</application>">
<!ENTITY calligrawords "<application>Calligra Words</application>">
<!ENTITY cantor "<application>Cantor</application>">
<!ENTITY CD	"<acronym>CD</acronym>">
<!ENTITY CDE	"<acronym>CDE</acronym>">
<!ENTITY CD-ROM	"<acronym>CD-ROM</acronym>">
<!ENTITY cervisia	"<application>Cervisia</application>">
<!ENTITY choqok	"<application>Choqok</application>">
<!ENTITY CIFS "<acronym>CIFS</acronym>">
<!ENTITY cmake '<application>CMake</application>'>
<!ENTITY CSS	"<acronym>CSS</acronym>">
<!ENTITY CUPS	"<acronym>CUPS</acronym>">
<!ENTITY DBus	"<acronym>D-Bus</acronym>">
<!ENTITY Debian	'<acronym><trademark class="registered">Debian</trademark></acronym>'>
<!ENTITY DOT	"<acronym>DOT</acronym>">
<!ENTITY DVD	"<acronym>DVD</acronym>">
<!ENTITY digikam	"<application>digiKam</application>">
<!ENTITY dolphin "<application>Dolphin</application>">
<!ENTITY dragon "<application>Dragon Player</application>">
<!ENTITY drkonqi	"<application>DrKonqi</application>">
<!ENTITY DVI	"<acronym>DVI</acronym>">
<!ENTITY ELF	"<acronym>ELF</acronym>">
<!ENTITY elisa	"<application>Elisa</application>">
<!ENTITY Emacs	"<application>Emacs</application>">
<!ENTITY falkon	"<application>Falkon</application>">
<!ENTITY Flash  "<trademark>Flash</trademark>">
<!ENTITY filelight "<application>Filelight</application>">
<!ENTITY firefox "<application>Firefox</application>">
<!ENTITY folder-config-location '<filename class="directory">$(qtpaths --paths GenericConfigLocation)</filename>'>
<!ENTITY folder-cache-location '<filename class="directory">$(qtpaths --paths GenericCacheLocation)</filename>'>
<!ENTITY folder-data-location '<filename class="directory">$(qtpaths --paths GenericDataLocation)</filename>'>
<!ENTITY FTP	"<acronym>FTP</acronym>">
<!ENTITY frameworks "<productname>Frameworks</productname>">
<!ENTITY gcc	"<command>gcc</command>">
<!ENTITY gcompris "<application>GCompris</application>">
<!ENTITY GIF	"<acronym>GIF</acronym>">
<!ENTITY git '<application>Git</application>'>
<!ENTITY GMT	"<acronym>GMT</acronym>">
<!ENTITY GNU	"<acronym>GNU</acronym>">
<!ENTITY gpg       "<application>GPG</application>">
<!ENTITY gpgsm     "<application>GpgSM</application>">
<!ENTITY granatier "<application>Granatier</application>">
<!ENTITY GUI	"<acronym>GUI</acronym>">
<!ENTITY gwenview '<application>Gwenview</application>'>
<!ENTITY Handspring	"<trademark>Handspring</trademark>">
<!ENTITY Hewlett-Packard	'<trademark class="registered">Hewlett-Packard</trademark>'>
<!ENTITY HotSync '<trademark class="registered">HotSync</trademark>'>
<!ENTITY HP	'<trademark class="registered">HP</trademark>'>
<!ENTITY HP-UX	'<trademark class="registered">HP-UX</trademark>'>
<!ENTITY HTML 	'<acronym>HTML</acronym>'>
<!ENTITY HTTP	"<acronym>HTTP</acronym>">
<!ENTITY IMAP "<acronym>IMAP</acronym>">
<!ENTITY infocenter     "<application>KInfoCenter</application>">
<!ENTITY ical	"<acronym>iCal</acronym>">
<!ENTITY irc "<acronym>IRC</acronym>">
<!ENTITY IRIX	'<trademark class="registered">IRIX</trademark>'>
<!ENTITY Java	"<trademark>Java</trademark>">
<!ENTITY javascript "<application>JavaScript</application>">
<!ENTITY Jini	"<trademark>Jini</trademark>">
<!ENTITY jovie  "<application>Jovie</application>">
<!ENTITY juk	"<application>JuK</application>">
<!ENTITY JSON	"<acronym>JSON</acronym>">
<!ENTITY kaddressbook	"<application>KAddressBook</application>">
<!ENTITY kaffeine "<application>Kaffeine</application>">
<!ENTITY kajongg  "<application>Kajongg</application>">
<!ENTITY kalarm	"<application>KAlarm</application>">
<!ENTITY kalarmd	"<application>KAlarmd</application>">
<!ENTITY kalgebra	"<application>KAlgebra</application>">
<!ENTITY kalzium	"<application>Kalzium</application>">
<!ENTITY kamera	"<application>Kamera</application>">
<!ENTITY kanagram	"<application>Kanagram</application>">
<!ENTITY kamoso     "<application>Kamoso</application>">
<!ENTITY kapman     "<application>Kapman</application>">
<!ENTITY kappfinder	"<application>Kappfinder</application>">
<!ENTITY kapptemplate	"<application>KAppTemplate</application>">
<!ENTITY karbon	"<application>Karbon</application>">
<!ENTITY kate	"<application>Kate</application>">
<!ENTITY katepart	"<application>KatePart</application>">
<!ENTITY katomic	"<application>KAtomic</application>">
<!ENTITY kaudiocreator "<application>KAudioCreator</application>">
<!ENTITY kbackgammon 	"<application>KBackgammon</application>">
<!ENTITY kbackup 	"<application>KBackup</application>">
<!ENTITY kbattleship 	"<application>KBattleship</application>">
<!ENTITY kbibtex "<application>KBibTeX</application>">
<!ENTITY kblackbox	"<application>KBlackBox</application>">
<!ENTITY kblocks	"<application>KBlocks</application>">
<!ENTITY kbounce	"<application>KBounce</application>">
<!ENTITY kbreakout	"<application>KBreakOut</application>">
<!ENTITY kbruch	"<application>KBruch</application>">
<!ENTITY kcachegrind	"<application>KCachegrind</application>">
<!ENTITY kcalc	"<application>KCalc</application>">
<!ENTITY kcharselect	"<application>KCharSelect</application>">
<!ENTITY kchart	"<application>KChart</application>">
<!ENTITY kcontrol	"<application>KControl</application>">
<!ENTITY kcron	"<application>KCron</application>">
<!ENTITY kde	'<orgname class="nonprofit">KDE</orgname>'>
<!ENTITY kdebugdialog	"<application>KDebugDialog</application>">
<!ENTITY kdebugdialog5	"<application>KDebugDialog</application>">
<!ENTITY kde-http	'<ulink url="http://www.kde.org/">http://www.kde.org/</ulink>'>
<!ENTITY kdepasswd	"<application>kdepasswd</application>">
<!ENTITY kdessh	"<application>kdessh</application>">
<!ENTITY kdesu	"<application>KDE su</application>">
<!ENTITY kdevelop "<application>KDevelop</application>">
<!ENTITY kdiamond	"<application>KDiamond</application>">
<!ENTITY kdiff3 "<application>KDiff3</application>">
<!ENTITY kdiskfree	"<application>KDiskFree</application>">
<!ENTITY keditbookmarks	"<application>KEditBookmarks</application>">
<!ENTITY kexi		"<application>Kexi</application>">
<!ENTITY kfax	"<application>KFax</application>">
<!ENTITY kfaxview	"<application>Kfaxview</application>">
<!ENTITY kfeeder 	"<application>KFeeder</application>">
<!ENTITY kfind	"<application>KFind</application>">
<!ENTITY kfloppy	"<application>Kfloppy</application>">
<!ENTITY kfontview "<application>KFontview</application>">
<!ENTITY kformula	"<application>KFormula</application>">
<!ENTITY kfouleggs	"<application>KFoulEggs</application>">
<!ENTITY kfourinline	"<application>KFourInLine</application>">
<!ENTITY kfract	"<application>KFract</application>">
<!ENTITY khangman	"<application>KHangMan</application>">
<!ENTITY kgeo	"<application>KGeo</application>">
<!ENTITY kgeography	"<application>KGeography</application>">
<!ENTITY kget	"<application>KGet</application>">
<!ENTITY kghostview	"<application>KGhostView</application>">
<!ENTITY kgoldrunner    "<application>KGoldrunner</application>">
<!ENTITY kgpg "<application>KGpg</application>">
<!ENTITY kgpgcertmanager "<application>KgpgCertManager</application>">
<!ENTITY kgraphviewer "<application>KGraphViewer</application>">
<!ENTITY khelpcenter	"<application>KHelpCenter</application>">
<!ENTITY kicker	"<application>Kicker</application>">
<!ENTITY kickoff "<application>Kickoff</application>">
<!ENTITY kiconedit	"<application>KIconEdit</application>">
<!ENTITY kig	"<application>Kig</application>">
<!ENTITY kigo  "<application>Kigo</application>">
<!ENTITY kikbd	"<application>kikbd</application>">
<!ENTITY kile "<application>Kile</application>">
<!ENTITY killbots "<application>Killbots</application>">
<!ENTITY kinfocenter    "<application>KInfoCenter</application>">
<!ENTITY kiriki	"<application>Kiriki</application>">
<!ENTITY Kirigami	"<application>Kirigami</application>">
<!ENTITY kit	"<application>Kit</application>">
<!ENTITY kitchensync        "<application>KitchenSync</application>">
<!ENTITY kiten	"<application>Kiten</application>">
<!ENTITY kivio	"<application>Kivio</application>">
<!ENTITY kjots	"<application>KJots</application>">
<!ENTITY kjumpingcube	"<application>KJumpingCube</application>">
<!ENTITY klaptop	"<application>KLaptop</application>">
<!ENTITY klatin		"<application>KLatin</application>">
<!ENTITY kleopatra "<application>Kleopatra</application>">
<!ENTITY klettres	"<application>KLettres</application>">
<!ENTITY klickety "<application>Klickety</application>">
<!ENTITY klipper	"<application>Klipper</application>">
<!ENTITY kmagnifier	"<application>KMagnifier</application>">
<!ENTITY kmahjongg	"<application>KMahjongg</application>">
<!ENTITY kmail	"<application>KMail</application>">
<!ENTITY kmathtool "<application>KMathTool</application>">
<!ENTITY kmenuedit	"<application>KMenuEdit</application>">
<!ENTITY kmessedwords	"<application>KMessedWords</application>">
<!ENTITY kmid	"<application>KMid</application>">
<!ENTITY kmidi	"<application>KMidi</application>">
<!ENTITY kmines	"<application>KMines</application>">
<!ENTITY kmix	"<application>KMix</application>">
<!ENTITY kmoon	"<application>KMoon</application>">
<!ENTITY kmouth "<application>KMouth</application>">
<!ENTITY kmousetool "<application>KMouseTool</application>">
<!ENTITY kmplayer "<application>KMPlayer</application>">
<!ENTITY kmplot	"<application>KmPlot</application>">
<!ENTITY kmymoney	"<application>KMyMoney</application>">
<!ENTITY knavalbattle "<application>KNavalBattle</application>">
<!ENTITY knetattach     "<application>KNetAttach</application>">
<!ENTITY knetwalk       "<application>KNetWalk</application>">
<!ENTITY knewsticker	"<application>KNewsTicker</application>">
<!ENTITY knewstuff "<application>KNewStuff</application>">
<!ENTITY knights "<application>Knights</application>">
<!ENTITY knode	"<application>KNode</application>">
<!ENTITY knotes	"<application>KNotes</application>">
<!ENTITY kolab  "<acronym>Kolab</acronym>">
<!ENTITY kolf	"<application>Kolf</application>">
<!ENTITY kollision "<application>Kollision</application>">
<!ENTITY kolorlines	"<application>Kolor Lines</application>">
<!ENTITY kolourpaint	"<application>KolourPaint</application>">
<!ENTITY kompare	"<application>Kompare</application>">
<!ENTITY konqueror	"<application>Konqueror</application>">
<!ENTITY konquest	"<application>Konquest</application>">
<!ENTITY konsole	"<application>Konsole</application>">
<!ENTITY konsolekalendar "<application>KonsoleKalendar</application>">
<!ENTITY kontact	"<application>Kontact</application>">
<!ENTITY konversation	"<application>Konversation</application>">
<!ENTITY kooka	"<application>Kooka</application>">
<!ENTITY kopete	"<application>Kopete</application>">
<!ENTITY korganizer	"<application>KOrganizer</application>">
<!ENTITY kpackage	"<application>KPackage</application>">
<!ENTITY kpager	"<application>KPager</application>">
<!ENTITY kpaint	"<application>KPaint</application>">
<!ENTITY kparts	"<acronym>KParts</acronym>">
<!ENTITY kpatience	"<application>KPatience</application>">
<!ENTITY kpercentage	"<application>KPercentage</application>">
<!ENTITY kpilot	"<application>KPilot</application>">
<!ENTITY kpf	"<application>kpf</application>">
<!ENTITY kpm	"<application>Kpm</application>">
<!ENTITY kpresenter	"<application>KPresenter</application>">
<!ENTITY kpoker	"<application>KPoker</application>">
<!ENTITY kpovmodeler	"<application>KPovModeler</application>">
<!ENTITY kppp	"<application>KPPP</application>">
<!ENTITY krdc	"<application>Remote Desktop Connection</application>">
<!ENTITY krecipes "<application>Krecipes</application>">
<!ENTITY kregexpeditor	"<application>KRegExpEditor</application>">
<!ENTITY krename "<application>KRename</application>">
<!ENTITY kreversi	"<application>KReversi</application>">
<!ENTITY krfb	"<application>Desktop Sharing</application>">
<!ENTITY krita	"<application>Krita</application>">
<!ENTITY kronometer	"<application>Kronometer</application>">
<!ENTITY kruler	"<application>KDE Screen Ruler</application>">
<!ENTITY krunner "<application>KRunner</application>">
<!ENTITY krusader '<application>Krusader</application>'>
<!ENTITY ksame	"<application>SameGame</application>">
<!ENTITY kscd	"<application>KsCD</application>">
<!ENTITY kscreensaver	"<application>KScreensaver</application>">
<!ENTITY ksgmltools	"<application>ksgmltools</application>">
<!ENTITY kshisen	"<application>Shisen-Sho</application>">
<!ENTITY ksim	"<application>KSim</application>">
<!ENTITY ksirc	"<application>KSirc</application>">
<!ENTITY ksirk '<application>KsirK</application>'>
<!ENTITY ksirtet	"<application>KSirtet</application>">
<!ENTITY ksmiletris	"<application>KSmiletris</application>">
<!ENTITY ksmserver      "<application>ksmserver</application>">
<!ENTITY ksnakeduel	"<application>KSnakeDuel</application>">
<!ENTITY ksnapshot	"<application>KSnapshot</application>">
<!ENTITY kspaceduel	"<application>KSpaceDuel</application>">
<!ENTITY sonnet "<application>Sonnet</application>">
<!ENTITY ksplash "<application>KSplash</application>">
<!ENTITY kspread	"<application>KSpread</application>">
<!ENTITY ksquares	"<application>KSquares</application>">
<!ENTITY kst	"<application>Kst</application>">
<!ENTITY kstars	"<application>KStars</application>">
<!ENTITY kstart	"<application>kstart</application>">
<!ENTITY ksysctrl	"<application>KSysctrl</application>">
<!ENTITY ksysguard	"<application>System Monitor</application>">
<!ENTITY ksystemlog  "<application>KSystemLog</application>">
<!ENTITY ktalk	"<application>KTalk</application>">
<!ENTITY ktalkd	"<application>KTalkd</application>">
<!ENTITY kteatime	"<application>KTeaTime</application>">
<!ENTITY kthesaurus	"<application>KThesaurus</application>">
<!ENTITY ktimemon	"<application>KTimemon</application>">
<!ENTITY ktimer	"<application>KTimer</application>">
<!ENTITY ktimetracker	"<application>KTimetracker</application>">
<!ENTITY ktip	"<application>KTip</application>">
<!ENTITY ktorrent "<application>KTorrent</application>">
<!ENTITY ktouch	"<application>KTouch</application>">
<!ENTITY ktron	"<application>KTron</application>">
<!ENTITY ktuberling	"<application>KTuberling</application>">
<!ENTITY kturtle	"<application>KTurtle</application>">
<!ENTITY kubrick "<application>Kubrick</application>">
<!ENTITY kubuntu	"<trademark>Kubuntu</trademark>">
<!ENTITY kuser	"<application>KUser</application>">
<!ENTITY kverbos "<application>KVerbos</application>">
<!ENTITY kvoctrain	"<application>KVocTrain</application>">
<!ENTITY kwallet "<application>KWallet</application>">
<!ENTITY kwallet5 "<application>KWallet</application>">
<!ENTITY kwalletmanager5 "<application>KWallet Manager</application>">
<!ENTITY kwatchgnupg "<application>KWatchGnuPG</application>">
<!ENTITY kwave "<application>Kwave</application>">
<!ENTITY kworldclock	"<application>KWorldClock</application>">
<!ENTITY kwin	"<application>KWin</application>">
<!ENTITY kwordquiz "<application>KWordQuiz</application>">
<!ENTITY kwrite	"<application>KWrite</application>">
<!ENTITY kwuftpd	"<application>kwuftpd</application>">
<!ENTITY kxkb	"<application>Kxkb</application>">
<!ENTITY kxstitch '<application>KXStitch</application>'>
<!ENTITY latex "L<superscript>A</superscript>T<subscript>E</subscript>X">
<!ENTITY LaserJet	'<trademark class="registered">LaserJet</trademark>'>
<!ENTITY lisa	"<application>Lisa</application>">
<!ENTITY Linux	'<trademark class="registered">Linux</trademark>'>
<!ENTITY LinuxPPC	"<trademark>LinuxPPC</trademark>">
<!ENTITY lokalize	"<application>Lokalize</application>">
<!ENTITY lskat	"<application>Lieutenant Skat</application>">
<!ENTITY LZW	"<acronym>LZW</acronym>">
<!ENTITY Mac	'<trademark class="registered">Mac</trademark>'>
<!ENTITY MacOS	'<trademark class="registered">Mac</trademark> <acronym>OS</acronym>'>
<!ENTITY macOS	'<trademark class="registered">macOS</trademark>'>
<!ENTITY marble "<application>Marble</application>">
<!ENTITY Markdown    "<acronym>Markdown</acronym>">
<!ENTITY MathML      "<acronym>MathML</acronym>">
<!ENTITY mdn         "<acronym>MDN</acronym>">
<!ENTITY megami	"<application>Megami</application>">
<!ENTITY Microsoft	'<trademark class="registered">Microsoft</trademark>'>
<!ENTITY MIDI	"<acronym>MIDI</acronym>">
<!ENTITY minuet	"<application>Minuet</application>">
<!ENTITY MIME	"<acronym>MIME</acronym>">
<!ENTITY Motif	'<trademark class="registered">Motif</trademark>'>
<!ENTITY MRU	"<acronym>MRU</acronym>">
<!ENTITY MTU	"<acronym>MTU</acronym>">
<!ENTITY multisynk  "<application>MultiSynk</application>">
<!ENTITY MusiXTeX	"<application>MusiXTeX</application>">
<!ENTITY NFS   "<acronym>NFS</acronym>">
<!ENTITY nntp  "<acronym>NNTP</acronym>">
<!ENTITY okteta "<application>Okteta</application>">
<!ENTITY okular	"<application>Okular</application>">
<!ENTITY openpgp  "<acronym>OpenPGP</acronym>">
<!ENTITY OSD	"<acronym>OSD</acronym>">
<!ENTITY palapeli "<application>Palapeli</application>">
<!ENTITY PalmOS	'<trademark class="registered">Palm OS</trademark>'>
<!ENTITY PalmPilot	"<productname><trademark>PalmPilot</trademark></productname>">
<!ENTITY parley	"<application>Parley</application>">
<!ENTITY PDF "<acronym>PDF</acronym>">
<!ENTITY phonon "<application>Phonon</application>">
<!ENTITY picmi "<application>Picmi</application>">
<!ENTITY PIM	"<acronym>PIM</acronym>">
<!ENTITY plasma "<productname>Plasma</productname>">
<!ENTITY plasmagik "<application>Plasmagik</application>">
<!ENTITY plasmoid "<application>Plasmoid</application>">
<!ENTITY plasmoids "<application>Plasmoids</application>">
<!ENTITY PMX	"<application>PMX</application>"><!-- acronym too? -->
<!ENTITY POP3 "<acronym>POP3</acronym>">
<!ENTITY PostScript	'<trademark class="registered">PostScript</trademark>'>
<!ENTITY PPP	"<acronym>PPP</acronym>">
<!ENTITY Qt	"<trademark>Qt</trademark>">
<!ENTITY RealAudio '<trademark class="registered">RealAudio</trademark>'>
<!ENTITY RealVideo '<trademark class="registered">RealVideo</trademark>'>
<!ENTITY RedHat	'<trademark class="registered">Red Hat</trademark>'>
<!ENTITY rocs "<application>Rocs</application>">
<!ENTITY rsibreak "<application>RSIBreak</application>">
<!ENTITY Samba "<application>Samba</application>">
<!ENTITY SMB   "<acronym>SMB</acronym>">
<!ENTITY skanlite "<application>Skanlite</application>">
<!ENTITY SDDM "<application>SDDM</application>">
<!ENTITY Sendmail	'<application><trademark class="registered">sendmail</trademark></application>'>
<!ENTITY smb4k "<application>Smb4K</application>">
<!ENTITY SMTP	"<acronym>SMTP</acronym>">
<!ENTITY SGI	"<trademark>SGI</trademark>">
<!ENTITY skladnik "<application>Skladnik</application>">
<!ENTITY skrooge '<application>Skrooge</application>'>
<!ENTITY smime  "<acronym>S/MIME</acronym>">
<!ENTITY snake	"<application>SnaKe</application>">
<!ENTITY Solaris	"<trademark>Solaris</trademark>">
<!ENTITY solid	"<application>Solid</application>">
<!ENTITY spectacle "<application>Spectacle</application>">
<!ENTITY step "<application>Step</application>">
<!ENTITY superkaramba	"<application>SuperKaramba</application>">
<!ENTITY SuSE	'<acronym><trademark class="registered">SuSE</trademark></acronym>'>
<!ENTITY SVG   "<acronym>SVG</acronym>">
<!ENTITY sweeper "<application>Sweeper</application>">
<!ENTITY symboleditor '<application>SymbolEditor</application>'>
<!ENTITY tellico '<application>Tellico</application>'>
<!ENTITY ThreeCom	'<trademark class="registered">3Com</trademark>'>
<!ENTITY TrueType	'<trademark class="registered">TrueType</trademark>'>
<!ENTITY trojita "<application>Trojitá</application>">
<!ENTITY ubuntu	"<trademark>Ubuntu</trademark>">
<!ENTITY umbrello	"<application>Umbrello</application>">
<!ENTITY UNIX	'<trademark class="registered">UNIX</trademark>'>
<!ENTITY URL	"<acronym>URL</acronym>">
<!ENTITY URI	"<acronym>URI</acronym>">
<!ENTITY USB	"<acronym>USB</acronym>">
<!ENTITY Visor	"<trademark>Visor</trademark>">
<!ENTITY Wayland	"<acronym>Wayland</acronym>">
<!ENTITY windowmaker	"<application>Window Maker</application>">
<!ENTITY Windows	'<trademark class="registered">Windows</trademark>'>
<!ENTITY WordNet   '<trademark class="registered">WordNet</trademark>'>
<!ENTITY Wordperfect '<application><trademark class="registered">WordPerfect</trademark></application>'>
<!ENTITY X11	"<application>X11</application>">
<!ENTITY X-Server	"<application>X-Server</application>">
<!ENTITY XEmacs	"<application>XEmacs</application>">
<!ENTITY X-Window '<trademark class="registered">X Window System</trademark>'>
<!ENTITY XHTML	"<acronym>XHTML</acronym>">
<!ENTITY XML	"<acronym>XML</acronym>">
<!ENTITY XSL	"<acronym>XSL</acronym>">

<!-- Defaults, do NOT add anything to this without checking with the docbook
     team.  -->


<!-- Deprecated, will be removed at a future time, do not use -->
<!ENTITY atlantik	"<application>Atlantik</application>">
<!ENTITY kde-ftp	'<ulink url="ftp://ftp.kde.org/pub/kde/">ftp://ftp.kde.org/pub/kde/</ulink>'>
<!ENTITY kdenlive	"<application>Kdenlive</application>">
<!ENTITY kdesvn	"<application>Kdesvn</application>">
<!ENTITY kuickshow	"<application>KuickShow</application>">
<!ENTITY Netscape	'<trademark class="registered">Netscape</trademark>'>
<!ENTITY package "This application isn't part of a KDE package">
