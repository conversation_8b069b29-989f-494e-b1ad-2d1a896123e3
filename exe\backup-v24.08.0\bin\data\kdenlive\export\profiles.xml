<profiles version="0.1">
 <group name="Generic (HD for web, mobile devices...)" renderer="avformat" type="av">
  <profile name="WebM-VP8/Vorbis (libre)" extension="webm"
   qualities="5,45" defaultquality="15"
   audioqualities="7,3" defaultaudioquality="5"
   args="f=webm vcodec=libvpx crf=%quality vb=8M qcomp=1 g=15 acodec=vorbis aq=%audioquality"/>
  <profile name="MP4-H264/AAC" extension="mp4"
   qualities="15,45" defaultquality="23"
   audiobitrates="256,64" defaultaudiobitrate="160"
   args="f=mp4 movflags=+faststart vcodec=libx264 crf=%quality g=15 acodec=aac ab=%audiobitrate+'k'"
   defaultspeedindex="6" speeds="preset=veryslow;preset=slower;preset=slow;preset=medium;preset=fast;preset=faster;preset=veryfast;preset=superfast;preset=ultrafast"/>
  <profile name="Matroska-H264/AAC" extension="mkv"
   qualities="15,45" defaultquality="23"
   audiobitrates="256,64" defaultaudiobitrate="160"
   args="f=matroska movflags=+faststart vcodec=libx264 crf=%quality g=15 acodec=aac ab=%audiobitrate+'k'"
   defaultspeedindex="6" speeds="preset=veryslow;preset=slower;preset=slow;preset=medium;preset=fast;preset=faster;preset=veryfast;preset=superfast;preset=ultrafast"/>
  <profile name="MPEG-2" extension="mpg"
   qualities="3,15" defaultquality="5"
   audioqualities="3,7" defaultaudioquality="3"
   args="properties=MPEG-2 qscale=%quality aq=%audioquality"
   speeds="subq=5 cmp=2 subcmp=2 trellis=1 bf=2;subq=3 cmp=1 subcmp=1 trellis=1 bf=2;subq=1"/>
  <profile name="GIF High Quality" extension="GIF" args=""/>
 </group>
 <group name="Ultra-High Definition (4K)" renderer="avformat" type="av">
  <profile name="WebM-VP9/Opus (libre)" extension="webm"
   qualities="15,45" defaultquality="25"
   audioqualities="10,0" defaultaudioquality="5"
   args="f=webm vcodec=libvpx-vp9 crf=%quality vb=15M qcomp=1 g=15 row-mt=1 tile-columns=4 frame-parallel=1 acodec=libopus compression_level=%audioquality"/>
  <profile name="WebM-AV1/Opus (libre)" extension="webm"
   qualities="15,45" defaultquality="25"
   audiobitrates="256,64" defaultaudiobitrate="160"
   args="f=webm vcodec=libaom-av1 crf=%quality vb=15M qcomp=1 g=15 row-mt=1 tiles=2x2 frame-parallel=1 strict=experimental acodec=libopus ab=%audiobitrate+'k'"/>
  <profile name="MP4-H265 (HEVC)" extension="mp4"
   qualities="15,45" defaultquality="28"
   audiobitrates="256,64" defaultaudiobitrate="160"
   args="properties=x265-medium f=mp4 vcodec=libx265 crf=%quality acodec=aac ab=%audiobitrate+'k'"
   defaultspeedindex="6" speeds="preset=veryslow;preset=slower;preset=slow;preset=medium;preset=fast;preset=faster;preset=veryfast;preset=superfast;preset=ultrafast"/>
 </group>
 <group name="Video with Alpha" renderer="avformat" type="av">
  <profile name="Alpha VP8" extension="webm"
   qualities="5,45" defaultquality="15"
   audioqualities="3,7" defaultaudioquality="4"
   args="f=webm vcodec=libvpx auto-alt-ref=0 crf=%quality vb=10M qcomp=1 g=15 mlt_image_format=rgba pix_fmt=yuva420p acodec=vorbis aq=%audioquality"/>
  <profile name="Alpha VP9" extension="webm"
   qualities="5,30" defaultquality="15"
   audioqualities="3,7" defaultaudioquality="4"
   args="f=webm vcodec=libvpx-vp9 crf=%quality vb=20M qcomp=1 g=15 mlt_image_format=rgba pix_fmt=yuva420p acodec=vorbis aq=%audioquality"/>
  <profile name="Alpha MOV" extension="mov" 
   args="f=mov vcodec=qtrle mlt_image_format=rgba pix_fmt=argb"/>
  <profile name="FFmpeg FFV1" extension="mkv"
   args="f=mov vcodec=ffv1 mlt_image_format=rgba pix_fmt=argb"/>
 </group>
 <group name="Old-TV definition (DVD...)" renderer="avformat" type="av">
  <profile name="VOB (DVD)" extension="vob"
   qualities="5,15" defaultquality="5"
   audiobitrates="192,128" defaultaudiobitrate="160"
   args="properties=dv_%dv_standard/DVD mlt_profile=dv_%dv_standard f=dvd vcodec=mpeg2video acodec=ac3 qscale=%quality ab=%audiobitrate+'k'"
   speeds="subq=5 cmp=2 subcmp=2 trellis=1 bf=2;subq=3 cmp=1 subcmp=1 trellis=1 bf=2;subq=1"/>
  <profile name="MPEG4-ASP/MP3 (DivX compatible)" extension="avi"
   qualities="5,15" defaultquality="5"
   audioqualities="3,7" defaultaudioquality="5"
   args="properties=MPEG-4-ASP qscale=%quality aq=%audioquality"
   speeds="subq=5 cmp=2 subcmp=2 trellis=1 bf=2;subq=3 cmp=1 subcmp=1 trellis=1 bf=2;subq=1"/>
  <profile name="Windows Media Player" extension="wmv"
   qualities="5,15" defaultquality="5"
   audioqualities="3,5,7" defaultaudioquality="5"
   args="f=asf vcodec=wmv2 acodec=wmav2 qscale=%quality aq=%audioquality"/>
 </group>
 <group name="Hardware Accelerated (experimental)" renderer="avformat" type="av">
  <profile name="NVENC AV1 VBR" extension="mp4"
   qualities="24,192" defaultquality="72"
   audiobitrates="256,64" defaultaudiobitrate="160"
   args="f=mp4 vcodec=av1_nvenc rc=constqp vqp=%quality vq=%quality acodec=aac ab=%audiobitrate+'k'"/>
  <profile name="NVENC H264 ABR" extension="mp4"
   bitrates="30000,1000" defaultbitrate="6000"
   audiobitrates="256,64" defaultaudiobitrate="160"
   args="f=mp4 vcodec=h264_nvenc vb=%bitrate+'k' acodec=aac ab=%audiobitrate+'k'"/>
  <profile name="NVENC H264 VBR" extension="mp4"
   qualities="15,45" defaultquality="23"
   audiobitrates="256,64" defaultaudiobitrate="160"
   args="f=mp4 vcodec=h264_nvenc rc=constqp vqp=%quality vq=%quality acodec=aac ab=%audiobitrate+'k'"/>
  <profile name="NVENC H265 ABR" extension="mp4"
   bitrates="30000,1000" defaultbitrate="6000"
   audiobitrates="256,64" defaultaudiobitrate="160"
   args="f=mp4 vcodec=hevc_nvenc vb=%bitrate+'k' acodec=aac ab=%audiobitrate+'k'"/>
  <profile name="VAAPI Intel H264" extension="mp4"
   args="f=mp4 vaapi_device=/dev/dri/renderD128 vf=’format=nv12,hwupload’ vcodec=h264_vaapi vb=30000k acodec=aac ab=192k"/>
  <profile name="VAAPI AMD H264" extension="mp4"
   args="f=mp4 hwaccel=vaapi hwaccel_device=renderD129 hwaccel_output_format=vaapi vcodec=h264_vaapi vb=30000k acodec=aac ab=192k"/>
 </group>
 <group name="Audio only" renderer="avformat" type="audio">
  <profile name="AC3" extension="ac3" audiobitrates="192,64" defaultaudiobitrate="160" args="ab=%audiobitrate+'k' vn=1 video_off=1"/>
  <profile name="ALAC" extension="mov" args="f=mov acodec=alac vn=1 video_off=1"/>
  <profile name="FLAC" extension="mka" args="f=matroska acodec=flac vn=1 video_off=1"/> 
  <profile name="MP3" extension="mp3" audioqualities="3,7" defaultaudioquality="5" args="aq=%audioquality acodec=libmp3lame vn=1 video_off=1"/>
  <profile name="OGG" extension="ogg" audioqualities="3,7" defaultaudioquality="4" args="aq=%audioquality vn=1 video_off=1"/>
  <profile name="WAV" extension="wav" args="properties=WAV video_off=1"/>
 </group>
 <group name="Images sequence" renderer="avformat" type="video">
  <profile name="PNG with alpha" extension="png" args="progressive=1 f=image2 vcodec=png an=1 audio_off=1 g=1 bg=0 pix_fmt=rgba"/>
 </group>
</profiles>
