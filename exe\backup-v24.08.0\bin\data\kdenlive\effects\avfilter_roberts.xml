<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.roberts" id="avfilter.roberts">
    <name><PERSON></name>
    <description>Apply roberts cross operator to input video stream</description>
    <author>libavfilter</author>
    <parameter type="list" name="av.planes" default="7" paramlist="0;1;2;3;4;5;6;7;8">
        <paramlistdisplay>None,Y,U,YU,V,YV,UV,YUV,Alpha</paramlistdisplay>
        <name>Planes</name>
    </parameter>
    <parameter type="constant" name="av.scale" default="10" min="1" max="250" factor="1">
        <name>Scale</name>
    </parameter>
    <parameter type="constant" name="av.delta" default="100" min="-300" max="300" factor="1">
        <name>Delta</name>
    </parameter>
</effect>
