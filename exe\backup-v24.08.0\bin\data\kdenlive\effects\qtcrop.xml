<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="qtcrop" id="qtcrop" type="video">
    <name>Crop by padding</name>
    <description>This filter crops the image to a rounded rectangle or circle by padding it with a color.</description>
    <author><PERSON></author>
    <parameter type="animatedrect" name="rect" default="0 0 %width %height" opacity="false">
        <name>Rectangle</name>
    </parameter>
    <parameter type="animated" name="radius" default="0" min="0" max="1" decimals="2">
        <name>Radius</name>
    </parameter>
    <parameter type="bool" name="circle" default="0">
        <name>Circle</name>
    </parameter>
    <parameter type="color" name="color" default="0x00000000" alpha="1">
        <name>Padding Color</name>
    </parameter>
</effect>
