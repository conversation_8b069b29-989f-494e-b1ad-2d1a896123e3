<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.histogram" id="avfilter.histogram">
    <name>Histogram</name>
    <description>Compute and draw a color distribution histogram for the input video</description>
    <author>libavfilter</author>
    <parameter type="constant" name="av.level_height" default="200" min="50" max="2048" factor="1">
        <name>Level height</name>
    </parameter>
    <parameter type="constant" name="av.scale_height" default="12" min="0" max="40" factor="1">
        <name>Scale height</name>
    </parameter>
    <parameter type="list" name="av.display_mode" default="stack" paramlist="overlay;stack;parade">
        <paramlistdisplay>Overlay,Stack,Parade</paramlistdisplay>
        <name>Display</name>
    </parameter>
    <parameter type="list" name="av.m" default="linear" paramlist="linear;logarithmic">
        <paramlistdisplay>Linear,Logarithmic</paramlistdisplay>
        <name>Mode</name>
    </parameter>
    <parameter type="list" name="av.c" default="1" paramlist="0;1;2;3;4;5;6;7;8">
        <paramlistdisplay>All,Y,U,YU,V,YV,UV,All,Alpha</paramlistdisplay>
        <name>Components to display</name>
    </parameter>
    <parameter type="constant" name="av.f" default="0.7" min="0" max="1" decimals="1">
        <name>Foreground Opacity</name>
    </parameter>
    <parameter type="constant" name="av.b" default="0.5" min="0" max="1" decimals="1">
        <name>Background Opacity</name>
    </parameter>
</effect>
