[Transcoding]
DNxHD 1080i 25 fps 185 Mb/s=-s 1920x1080 -r pal -top -1 -flags +ilme+ildct -vb 185000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 1080p 25 fps 185 Mb/s=-s 1920x1080 -r 25 -vb 185000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 1080p 24 fps 175 Mb/s=-s 1920x1080 -r 24 -vb 175000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 1080p 23.976 fps 175 Mb/s=-s 1920x1080 -r 23.976 -vb 175000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 1080i 25 fps 120 Mb/s=-s 1920x1080 -r pal -top -1 -flags +ilme+ildct -vb 120000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 1080p 25 fps 120 Mb/s=-s 1920x1080 -r 25 -vb 120000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 1080p 24 fps 115 Mb/s=-s 1920x1080 -r 24 -vb 115000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 1080p 23.976 fps 115 Mb/s=-s 1920x1080 -r 23.976 -vb 115000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 1080i 30 fps 220 Mb/s=-s 1920x1080 -r ntsc -top -1 -flags +ilme+ildct -vb 220000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 1080p 30 fps 220 Mb/s=-s 1920x1080 -r 30 -vb 220000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 1080i 30 fps 145 Mb/s=-s 1920x1080 -r ntsc -top -1 -flags +ilme+ildct -vb 145000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 1080p 30 fps 145 Mb/s=-s 1920x1080 -r 30 -vb 145000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 720p 23.976 fps 90 Mb/s=-s 1280x720 -r 24000/1001 -vb 90000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 720p 23.976 fps 60 Mb/s=-s 1280x720 -r 24000/1001 -vb 60000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 720p 50 fps 175 Mb/s=-s 1280x720 -r 50 -vb 175000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 720p 50 fps 115 Mb/s=-s 1280x720 -r 50 -vb 115000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 720p 59.94 fps 220 Mb/s=-s 1280x720 -r 60000/1001 -vb 220000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
DNxHD 720p 59.94 fps 145 Mb/s=-s 1280x720 -r 60000/1001 -vb 145000k -threads 0 -vcodec dnxhd -acodec pcm_s16le -ar 48000 %1.mov;High quality encoding
Fix MPEG-1=-sameq -acodec copy -vcodec mpeg1video %1.mpg;Fix unplayable MPEG-1 files;;vcodec=mpeg1video
Fix Ogg Theora=-sameq -vcodec libtheora -acodec copy %1.ogv;Fix unplayable OGG Theora files;;vcodec=theora
ProRes 422 Proxy=-vcodec prores_ks -profile:v 0 -acodec pcm_s16le -ar 48000 -ac 2 %1.mov;High quality encoding
ProRes 422 LT=-vcodec prores_ks -profile:v 1 -acodec pcm_s16le -ar 48000 -ac 2 %1.mov;High quality encoding
ProRes 422 SQ=-vcodec prores_ks -profile:v 2 -acodec pcm_s16le -ar 48000 -ac 2 %1.mov;High quality encoding
ProRes 422 HQ=-vcodec prores_ks -profile:v 3 -acodec pcm_s16le -ar 48000 -ac 2 %1.mov;High quality encoding
ProRes 444 with alpha=-vcodec prores_ks -profile:v 4 -pix_fmt yuva444p10le -bits_per_mb 8000 -acodec pcm_s16le -ar 48000 -ac 2 %1.mov;High quality encoding with alpha channel
Remux MPEG-2 PS/VOB=-vcodec copy -acodec copy %1.mpg;Fix audio sync in MPEG-2 vob files;;vcodec=mpeg2video
Remux MPEG-2 PS/VOB=-vcodec copy -acodec copy %1.mpg;Fix audio sync in MPEG-2 vob files 2;
Lossless Matroska=-sn -vcodec huffyuv -acodec flac %1.mkv;High quality lossless encoding
Wav 48000Hz=-vn -ar 48000 %1.wav;Extract audio as WAV file;audio
Remux with MKV=-vcodec copy -acodec copy -sn %1.mkv
DVD PAL 4:3=-f dvd -r 25 -vf scale=720:576 -aspect 4:3 -minrate 0 -maxrate 8000k -muxrate 10080000 -g 15 -bufsize 1835008 -packetsize 2048 -trellis 1 -me_range 63 -acodec ac3 -ab 192k -ar 48000 -vcodec mpeg2video -vb 5000k %1.vob;Dvd PAL
DVD PAL 16:9=-f dvd -r 25 -vf scale=720:576 -aspect 16:9 -minrate 0 -maxrate 8000k -muxrate 10080000 -g 15 -bufsize 1835008 -packetsize 2048 -trellis 1 -me_range 63 -acodec ac3 -ab 192k -ar 48000 -vcodec mpeg2video -vb 5000k %1.vob;Dvd PAL wide
DVD NTSC 4:3=-f dvd -r 23.976 -vf scale=720:480 -aspect 4:3 -minrate 0 -maxrate 9000k -muxrate 10080000 -g 18 -bufsize 1835008 -packetsize 2048 -trellis 1 -me_range 63 -acodec ac3 -ab 192k -ar 48000 -vcodec mpeg2video -vb 6000k %1.vob;Dvd NTSC
DVD NTSC 16:9=-f dvd -r 23.976 -vf scale=720:480 -aspect 16:9 -minrate 0 -maxrate 9000k -muxrate 10080000 -g 18 -bufsize 1835008 -packetsize 2048 -trellis 1 -me_range 63 -acodec ac3 -ab 192k -ar 48000 -vcodec mpeg2video -vb 6000k %1.vob;Dvd NTSC wide

[intermediate]
AAC (Audio only)=-vn -codec:a aac -ab 256k %1.m4a;audio
Lossy x264 I frame only=-f mp4 -codec:v libx264 -g 1 -bf 0 -crf 15 -preset medium -codec:a aac -ab 256k %1.mp4;av
Lossy x264 I frame only (VAAPI GPU)=-init_hw_device vaapi=vaapi0: -filter_hw_device vaapi0 -i -vf format=nv12,hwupload -f mp4 -c:v h264_vaapi -g 1 -bf 0 -crf 15 -preset medium -codec:a aac -ab 256k %1.mp4;av
Lossy x264 I frame only (NVidia GPU)=-f mp4 -vsync 0 -codec:v h264_nvenc -vb 0 -rc cbr -g 2 -bf 0 -codec:a aac -ab 256k %1.mp4;av
Lossy x264 I frame only (Video only)=-f mp4 -codec:v libx264 -g 1 -bf 0 -crf 15 -preset medium -an %1.mp4;video
Lossy x264 I frame only (VAAPI GPU Video only)=-init_hw_device vaapi=vaapi0: -filter_hw_device vaapi0 -i -vf format=nv12,hwupload -f mp4 -c:v h264_vaapi -g 1 -bf 0 -crf 15 -preset medium -an %1.mp4;video
Lossy x264 I frame only (NVidia GPU Video only)=-f mp4 -vsync 0 -codec:v h264_nvenc -vb 0 -rc cbr -g 2 -bf 0 -an %1.mp4;video
Intermediate DNxHR HQ (Large files)=-f mov -codec:a pcm_f32le -codec:v dnxhd -profile:v dnxhr_hq -pix_fmt yuv422p %1.mov;av
Lossless (Huge files)=-f matroska -codec:a pcm_f32le -codec:v utvideo -pix_fmt yuv422p %1.mkv;av
