<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.median" id="avfilter.median">
    <name>Median</name>
    <description>Pick median pixel from certain rectangle defined by radius.</description>
    <author>libavfilter</author>
    <parameter type="animated" name="av.radius" min="1" max="127" default="1" decimals="3">
        <name>Spatial sigma</name>
        <comment>Median radius</comment>
    </parameter>
    <parameter type="animated" name="av.radiusV" min="0" max="127" default="0" decimals="3">
        <name>Median vertical radius</name>
    </parameter>
    <parameter type="animated" name="av.percentile" min="0" max="1" default="0.5" decimals="3">
        <name>Median percentile</name>
    </parameter>
    <parameter type="list" name="av.planes" default="1" paramlist="0;1;2;3;4;5;6;7;">
        <paramlistdisplay>Alpha,Y,U,V,Red,Green,Blue,All,</paramlistdisplay>
        <name>Planes</name>
    </parameter>
</effect>
