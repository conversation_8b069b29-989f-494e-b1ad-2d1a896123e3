<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.crystalizer" id="avfilter.crystalizer" type="audio">
    <name>Crystalizer</name>
    <description>Simple algorithm for audio noise sharpening.
This filter linearly increases differences betweeen each audio sample.</description>
    <author>libavfilter</author>
    <parameter type="constant" name="av.i" default="2" min="-10" max="10" decimals="2">
        <name>Intensity</name>
        <comment>Sets the intensity of effect (default: 2.0).
Must be in range between -10.0 to 0 (unchanged sound) to 10.0 (maximum effect).
To inverse filtering use negative value. </comment>
    </parameter>
    <parameter type="bool" name="av.c" default="1">
        <name>Enable clipping</name>
    </parameter>
</effect>
