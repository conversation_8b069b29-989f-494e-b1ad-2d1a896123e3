<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.highshelf" id="avfilter.highshelf" type="audio">
    <name>High-shelf</name>
    <description>Apply a high shelf filter.</description>
    <author>libavfilter</author>
    <parameter type="constant" name="av.f" default="3000" min="20" max="20000" suffix="Hz">
        <name>Central frequency</name>
    </parameter>
    <parameter type="list" name="av.t" default="h" paramlist="h;q;o;s;k">
        <paramlistdisplay>Hz,Q-FActor,Octave,Slope,KHz</paramlistdisplay>
        <name>Type</name>
    </parameter>
    <parameter type="constant" name="av.w" default="0.5" min="0.1" max="9999" decimals="1">
        <name>Filter-width</name>
    </parameter>
    <parameter type="constant" name="av.g" default="0" min="-60" max="60" suffix="dB">
        <name>Gain</name>
    </parameter>
    <parameter type="list" name="av.p" default="2" paramlist="1;2">
        <paramlistdisplay>1,2</paramlistdisplay>
        <name>Poles</name>
    </parameter>
    <parameter type="constant" name="av.m" default="1" min="0" max="1" decimals="2">
        <name>Mix</name>
    </parameter>
    <parameter type="bool" name="av.n" default="0">
        <name>Normalize</name>
    </parameter>
    <parameter type="list" name="av.a" default="di" paramlist="di;dii;tdii;latt">
        <paramlistdisplay>di,dii,tdii,latt</paramlistdisplay>
        <name>Transform type</name>
    </parameter>
    <parameter type="list" name="av.r" default="auto" paramlist="auto;s16;s32;f32;f64">
        <paramlistdisplay>Auto,s16,s32,f32,f64</paramlistdisplay>
        <name>Filter precision</name>
    </parameter>
</effect>
