<?xml version="1.0"?>
<!DOCTYPE kpartgui>
<effect tag="avfilter.highpass" id="avfilter.highpass" type="audio">
    <name>High-pass</name>
    <description>Apply a high-pass filter with 3dB point frequency.</description>
    <author>libavfilter</author>
    <parameter type="constant" name="av.f" default="3000" min="20" max="20000" suffix="Hz">
        <name>Central frequency</name>
    </parameter>
    <parameter type="list" name="av.t" default="h" paramlist="h;q;o;s;k">
        <paramlistdisplay>Hz,Q-FActor,Octave,Slope,KHz</paramlistdisplay>
        <name>Type</name>
    </parameter>
    <parameter type="constant" name="av.w" default="707" min="100" max="99999" decimals="3">
        <name>Filter-width</name>
    </parameter>
    <parameter type="list" name="av.p" default="2" paramlist="1;2">
        <paramlistdisplay>1,2</paramlistdisplay>
        <name>Poles</name>
    </parameter>
    <parameter type="constant" name="av.m" default="1" min="0" max="1" decimals="2">
        <name>Mix</name>
    </parameter>
    <parameter type="bool" name="av.n" default="0">
        <name>Normalize</name>
    </parameter>
    <parameter type="list" name="av.a" default="di" paramlist="di;dii;tdii;latt">
        <paramlistdisplay>di,dii,tdii,latt</paramlistdisplay>
        <name>Transform type</name>
    </parameter>
    <parameter type="list" name="av.r" default="auto" paramlist="auto;s16;s32;f32;f64">
        <paramlistdisplay>Auto,s16,s32,f32,f64</paramlistdisplay>
        <name>Filter precision</name>
    </parameter>
</effect>
